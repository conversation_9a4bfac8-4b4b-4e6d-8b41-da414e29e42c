import { bus } from "./bus"

// WebSocket消息类型定义
export interface WSMessage {
  type: string
  user_id: number
  data: any
  timestamp: number
}

// 容量更新数据类型
export interface CapacityUpdateData {
  base_path_id: number
  base_path: string
  status: string // "calculating", "completed", "failed"
  used_bytes: number
  total_files: number
  progress: number // 0.0-1.0
}

// 容量超限通知数据类型
export interface CapacityExceededData {
  folder_name: string
  storage_type: string
  base_path: string
  deleted_files: string[]
}

// WebSocket连接状态
export enum WSConnectionState {
  CONNECTING = "connecting",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  RECONNECTING = "reconnecting",
  FAILED = "failed"
}

class WebSocketManager {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000 // 初始重连延迟1秒
  private maxReconnectDelay = 30000 // 最大重连延迟30秒
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private heartbeatInterval = 30000 // 心跳间隔30秒
  private connectionState = WSConnectionState.DISCONNECTED
  private messageHandlers: Map<string, ((data: any) => void)[]> = new Map()

  constructor() {
    this.connect()
  }

  // 获取WebSocket URL
  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:"

    // 在开发环境中，前端运行在5173端口，需要指向后端端口5244
    let host = window.location.host
    if (window.location.port === "5173") {
      // 开发环境，指向后端端口5244
      host = window.location.hostname + ":5244"
    }

    const token = localStorage.getItem("token") || ""
    if (!token) {
      console.warn("⚠️ Token为空，无法建立WebSocket连接")
      throw new Error("Token为空，无法建立WebSocket连接")
    }

    const url = `${protocol}//${host}/api/ws?token=${encodeURIComponent(token)}`
    console.log("🔗 WebSocket连接URL:", url.replace(token, "***"))
    console.log("🔗 当前页面端口:", window.location.port, "目标主机:", host)
    return url
  }

  // 连接WebSocket
  connect() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return
    }

    // 检查token是否存在
    const token = localStorage.getItem("token")
    if (!token) {
      console.warn("⚠️ 用户未登录，跳过WebSocket连接")
      return
    }

    this.connectionState = WSConnectionState.CONNECTING
    console.log("🔌 正在连接WebSocket...")

    try {
      this.ws = new WebSocket(this.getWebSocketUrl())
      this.setupEventHandlers()
    } catch (error) {
      console.error("❌ WebSocket连接失败:", error)
      this.handleConnectionError()
    }
  }

  // 设置事件处理器
  private setupEventHandlers() {
    if (!this.ws) return

    this.ws.onopen = () => {
      console.log("✅ WebSocket连接成功")
      console.log("🔗 连接详情:", {
        url: this.ws?.url,
        readyState: this.ws?.readyState,
        protocol: this.ws?.protocol
      })
      this.connectionState = WSConnectionState.CONNECTED
      this.reconnectAttempts = 0
      this.reconnectDelay = 1000
      this.startHeartbeat()
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WSMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error("❌ WebSocket消息解析失败:", error, event.data)
      }
    }

    this.ws.onclose = (event) => {
      console.log("🔌 WebSocket连接关闭:", event.code, event.reason)
      console.log("🔌 关闭事件详情:", {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        type: event.type
      })
      this.connectionState = WSConnectionState.DISCONNECTED
      this.stopHeartbeat()

      // 如果不是主动关闭，尝试重连
      if (event.code !== 1000) {
        this.handleConnectionError()
      }
    }

    this.ws.onerror = (error) => {
      console.error("❌ WebSocket连接错误:", error)
      console.error("❌ 错误详情:", {
        type: error.type,
        target: error.target,
        currentTarget: error.currentTarget
      })
      this.handleConnectionError()
    }
  }

  // 处理连接错误
  private handleConnectionError() {
    this.connectionState = WSConnectionState.FAILED
    this.stopHeartbeat()

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      this.connectionState = WSConnectionState.RECONNECTING
      
      console.log(`🔄 WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      this.reconnectTimer = window.setTimeout(() => {
        this.connect()
      }, this.reconnectDelay)

      // 指数退避，但不超过最大延迟
      this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.maxReconnectDelay)
    } else {
      console.error("❌ WebSocket重连失败，已达到最大重试次数")
      this.connectionState = WSConnectionState.FAILED
    }
  }

  // 处理收到的消息
  private handleMessage(message: WSMessage) {
    console.log("📨 收到WebSocket消息:", message)

    // 处理心跳响应
    if (message.type === "pong") {
      return
    }

    // 处理容量更新消息
    if (message.type === "capacity_update") {
      const data = message.data as CapacityUpdateData
      console.log("📊 收到容量更新消息:", data)

      // 通过事件总线广播容量更新
      bus.emit("capacity_update", {
        base_path_id: data.base_path_id,
        base_path: data.base_path,
        status: data.status,
        used_bytes: data.used_bytes,
        total_files: data.total_files,
        progress: data.progress
      })
    }

    // 处理容量超限通知消息
    if (message.type === "capacity_exceeded") {
      const data = message.data as CapacityExceededData
      console.log("🚨 收到容量超限通知:", data)

      // 动态导入notify模块并显示通知
      import("./notify").then(({ notify }) => {
        notify.capacityExceeded(data.folder_name, data.storage_type)
      }).catch(error => {
        console.error("❌ 导入notify模块失败:", error)
      })
    }

    // 调用注册的消息处理器
    const handlers = this.messageHandlers.get(message.type) || []
    handlers.forEach(handler => {
      try {
        handler(message.data)
      } catch (error) {
        console.error("❌ WebSocket消息处理器执行失败:", error)
      }
    })
  }

  // 发送消息
  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn("⚠️ WebSocket未连接，无法发送消息:", message)
    }
  }

  // 开始心跳
  private startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = window.setInterval(() => {
      this.send({ type: "ping", timestamp: Date.now() })
    }, this.heartbeatInterval)
  }

  // 停止心跳
  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 注册消息处理器
  onMessage(type: string, handler: (data: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type)!.push(handler)
  }

  // 移除消息处理器
  offMessage(type: string, handler: (data: any) => void) {
    const handlers = this.messageHandlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  // 断开连接
  disconnect() {
    console.log("🔌 主动断开WebSocket连接")
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, "主动断开")
      this.ws = null
    }
    
    this.connectionState = WSConnectionState.DISCONNECTED
  }

  // 获取连接状态
  getConnectionState(): WSConnectionState {
    return this.connectionState
  }

  // 检查是否已连接
  isConnected(): boolean {
    return this.connectionState === WSConnectionState.CONNECTED
  }
}

// 全局WebSocket管理器实例
let globalWebSocketManager: WebSocketManager | null = null

// 获取全局WebSocket管理器
export function getWebSocketManager(): WebSocketManager {
  if (!globalWebSocketManager) {
    globalWebSocketManager = new WebSocketManager()
  }
  return globalWebSocketManager
}

// 初始化WebSocket连接
export function initWebSocket() {
  console.log("🚀 初始化WebSocket服务")
  // 不立即连接，等待用户登录后再连接
}

// 用户登录后连接WebSocket
export function connectWebSocket() {
  console.log("🔌 用户已登录，开始连接WebSocket")
  getWebSocketManager()
}

// 清理WebSocket连接
export function cleanupWebSocket() {
  if (globalWebSocketManager) {
    globalWebSocketManager.disconnect()
    globalWebSocketManager = null
  }
}

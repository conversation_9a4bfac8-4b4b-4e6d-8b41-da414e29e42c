# Chrome MCP Server 安装说明

## 问题解决状态
✅ **已完成的步骤：**
1. 安装了正确的 `mcp-chrome-bridge` 包
2. 下载并解压了Chrome扩展到 `chrome-extension/` 目录
3. 注册了Native Messaging host
4. 更新了MCP配置文件

## 需要手动完成的步骤

### 1. 安装Chrome扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 启用右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目目录下的 `chrome-extension` 文件夹
6. 扩展安装成功后，会在浏览器工具栏显示扩展图标

### 2. 连接MCP服务器
1. 点击Chrome扩展图标
2. 在弹出的界面中点击"连接"按钮
3. 如果连接成功，会显示MCP配置信息

### 3. 验证配置
当Chrome扩展连接成功后，HTTP服务器会在 `http://127.0.0.1:12306/mcp` 启动，此时Amazon Q就能连接到Chrome MCP Server了。

## 当前MCP配置
```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": [
        "@modelcontextprotocol/server-fetch",
        "https://server.smithery.ai/@upstash/context7-mcp/mcp?api_key=9c2798fb-a106-460d-83cf-c83d4c3ad38d&profile=metropolitan-tiger-ZQvY5Q"
      ],
      "env": {},
      "timeout": 120000,
      "disabled": false
    },
    "memory-bank-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@aakarsh-sasi/memory-bank-mcp",
        "--key",
        "9c2798fb-a106-460d-83cf-c83d4c3ad38d"
      ],
      "env": {},
      "timeout": 120000,
      "disabled": false
    },
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp",
      "timeout": 120000,
      "disabled": false
    }
  }
}
```

## 故障排除
如果遇到问题：
1. 确保Chrome扩展已正确安装并启用
2. 检查扩展是否已连接（点击扩展图标查看状态）
3. 重启Amazon Q CLI
4. 如果仍有问题，可以临时禁用chrome-mcp-server（设置 "disabled": true）

## 扩展功能
安装成功后，你可以使用以下功能：
- 浏览器标签页管理
- 网页截图
- 网络请求监控
- 内容分析和语义搜索
- 自动化交互操作
- 书签和历史记录管理

完成Chrome扩展安装后，所有MCP服务器应该都能正常工作了！

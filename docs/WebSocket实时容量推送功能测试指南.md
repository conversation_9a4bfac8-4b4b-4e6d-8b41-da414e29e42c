# WebSocket实时容量推送功能测试指南

## 📋 测试概述

本指南详细说明如何测试WebSocket实时容量推送功能，确保分享转存后容量卡片能够实时更新，无需手动刷新页面。

## 🔧 测试前准备

### 1. 环境要求
- ✅ 后端服务正常运行
- ✅ 前端已重新构建并部署
- ✅ 用户账号具有分享转存权限
- ✅ 浏览器支持WebSocket（现代浏览器均支持）

### 2. 测试数据准备
- 准备有效的百度网盘Cookie
- 准备测试用的分享链接（建议包含不同大小的文件）
- 确保用户有足够的存储空间配额

### 3. 开发者工具设置
- 打开浏览器开发者工具（F12）
- 切换到Console标签查看日志
- 切换到Network标签监控WebSocket连接

## 🧪 功能测试步骤

### 测试1：WebSocket连接建立
**目标**：验证WebSocket连接能够正常建立

**步骤**：
1. 登录AList系统
2. 打开浏览器开发者工具 → Network标签
3. 筛选WS（WebSocket）连接
4. 观察是否有到 `/api/ws` 的WebSocket连接

**预期结果**：
- ✅ 看到WebSocket连接状态为"101 Switching Protocols"
- ✅ 控制台输出："🔌 正在连接WebSocket..."
- ✅ 控制台输出："✅ WebSocket连接成功"

### 测试2：容量卡片初始状态
**目标**：确认容量卡片正常显示

**步骤**：
1. 查看首页左侧边栏
2. 确认容量卡片显示正常
3. 记录当前的容量信息

**预期结果**：
- ✅ 容量卡片正常显示
- ✅ 显示当前已使用容量和总容量
- ✅ 进度条颜色和百分比正确

### 测试3：小文件转存实时更新
**目标**：验证小文件转存后的实时更新

**步骤**：
1. 点击右下角悬浮按钮 → "分享转存"
2. 输入有效的百度网盘Cookie
3. 输入一个小文件（<50MB）的分享链接
4. 点击"开始转存"
5. 观察容量卡片变化

**预期结果**：
- ✅ 转存成功后2-3秒内容量卡片自动更新
- ✅ 控制台输出："📊 收到WebSocket容量更新"
- ✅ 控制台输出："🔄 更新基础路径容量"
- ✅ 容量数据准确反映新增文件

### 测试4：大文件转存实时更新
**目标**：验证大文件转存后的实时更新（这是原问题的核心）

**步骤**：
1. 准备一个大文件（>200MB）的分享链接
2. 执行分享转存操作
3. 观察容量卡片在计算过程中的状态变化
4. 等待计算完成，观察最终更新

**预期结果**：
- ✅ 转存成功后显示"正在计算容量..."状态
- ✅ 计算完成后容量卡片立即更新（无需6秒等待）
- ✅ 控制台输出WebSocket推送消息
- ✅ 最终容量数据准确

### 测试5：多文件批量转存
**目标**：验证批量转存的实时更新

**步骤**：
1. 准备多个分享链接（3-5个）
2. 一次性输入所有链接进行转存
3. 观察容量卡片的更新过程

**预期结果**：
- ✅ 每个文件转存完成后都会触发容量更新
- ✅ 容量卡片实时反映累计变化
- ✅ 所有文件转存完成后容量数据准确

### 测试6：网络断开重连
**目标**：验证网络断开后的自动重连功能

**步骤**：
1. 在开发者工具Network标签中找到WebSocket连接
2. 右键点击 → "Close connection"手动断开
3. 观察重连过程
4. 执行一次分享转存测试重连后的功能

**预期结果**：
- ✅ 控制台输出："🔄 WebSocket重连中..."
- ✅ 自动重新建立连接
- ✅ 重连后功能正常

## 🔍 调试和排错

### 常见问题排查

#### 问题1：WebSocket连接失败
**症状**：控制台显示连接错误
**排查步骤**：
1. 检查后端服务是否正常运行
2. 确认用户已正确登录
3. 检查网络连接
4. 查看后端日志是否有错误

#### 问题2：容量卡片不更新
**症状**：转存成功但容量卡片没有变化
**排查步骤**：
1. 检查WebSocket连接状态
2. 查看控制台是否收到推送消息
3. 确认转存是否真正成功
4. 检查基础路径配置

#### 问题3：更新延迟过长
**症状**：容量更新延迟超过预期
**排查步骤**：
1. 检查文件大小和计算复杂度
2. 查看后端容量计算日志
3. 确认服务器性能状况
4. 检查网络延迟

### 日志监控要点

#### 前端关键日志
```
🚀 App组件启动，初始化WebSocket连接
🔌 正在连接WebSocket...
✅ WebSocket连接成功
📊 收到WebSocket容量更新
🔄 更新基础路径容量
```

#### 后端关键日志
```
WebSocket客户端注册成功: UserID=1, ClientID=xxx
开始更新分享基础路径容量: /path, 增量: 1024 bytes
发送容量更新消息: UserID=1, BasePath=/path, Status=completed
```

## 📊 性能测试

### 响应时间测试
**目标**：验证实时推送的响应速度

**测试方法**：
1. 记录转存完成时间
2. 记录容量卡片更新时间
3. 计算时间差

**预期指标**：
- WebSocket推送延迟 < 100ms
- 前端更新响应 < 50ms
- 总体延迟 < 200ms

### 并发测试
**目标**：验证多用户同时使用的稳定性

**测试方法**：
1. 多个用户同时登录
2. 同时执行分享转存操作
3. 观察各用户的容量更新情况

**预期结果**：
- 各用户独立更新，互不影响
- 推送消息准确送达对应用户
- 系统性能稳定

## ✅ 测试验收标准

### 功能验收
- [ ] WebSocket连接建立成功率 > 99%
- [ ] 小文件转存实时更新成功率 > 99%
- [ ] 大文件转存实时更新成功率 > 95%
- [ ] 网络断开自动重连成功率 > 90%

### 性能验收
- [ ] 容量更新延迟 < 200ms
- [ ] WebSocket连接稳定性 > 99%
- [ ] 内存泄漏检测通过
- [ ] 长时间运行稳定性测试通过

### 用户体验验收
- [ ] 无需手动刷新页面
- [ ] 状态反馈清晰直观
- [ ] 错误处理友好
- [ ] 操作流程自然流畅

## 🎯 测试报告模板

### 测试环境
- 操作系统：
- 浏览器版本：
- 后端版本：
- 测试时间：

### 测试结果
| 测试项目 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| WebSocket连接 | 成功建立 | | ✅/❌ |
| 小文件转存 | 实时更新 | | ✅/❌ |
| 大文件转存 | 实时更新 | | ✅/❌ |
| 网络重连 | 自动恢复 | | ✅/❌ |

### 问题记录
- 问题描述：
- 重现步骤：
- 解决方案：

---

**测试指南版本**：v1.0  
**创建时间**：2025年1月19日  
**适用版本**：AList v3.x + WebSocket实时推送功能

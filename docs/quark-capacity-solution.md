# 夸克网盘容量获取问题解决方案

## 问题概述
夸克网盘驱动在 AList 中可以正常工作，但无法获取容量信息，显示为 0/0 bytes。

## 解决方案代码

### 1. 增强版容量获取函数
```go
package quark

import (
    "encoding/json"
    "fmt"
    "net/http"
    "strings"
    "time"
)

// 增强版容量信息获取
func (d *Quark) GetCapacityWithRetry() (*CapacityInfo, error) {
    // 方案1: 尝试网页端API
    if capacity, err := d.tryWebAPI(); err == nil {
        return capacity, nil
    }
    
    // 方案2: 尝试移动端API（带完整参数）
    if capacity, err := d.tryMobileAPIWithParams(); err == nil {
        return capacity, nil
    }
    
    // 方案3: 文件大小估算
    if capacity, err := d.estimateFromFiles(); err == nil {
        return capacity, nil
    }
    
    // 方案4: 返回默认值
    return &CapacityInfo{
        Used:  0,
        Total: 10 * 1024 * 1024 * 1024 * 1024, // 10TB 默认
    }, nil
}

// 网页端API尝试
func (d *Quark) tryWebAPI() (*CapacityInfo, error) {
    client := &http.Client{Timeout: 10 * time.Second}
    
    req, err := http.NewRequest("GET", "https://pan.quark.cn/1/clouddrive/capacity", nil)
    if err != nil {
        return nil, err
    }
    
    // 设置完整的浏览器头部
    req.Header.Set("Cookie", d.Cookie)
    req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
    req.Header.Set("Referer", "https://pan.quark.cn/")
    req.Header.Set("Accept", "application/json, text/plain, */*")
    req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
    
    resp, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != 200 {
        return nil, fmt.Errorf("API returned status %d", resp.StatusCode)
    }
    
    var result struct {
        Success bool `json:"success"`
        Data    struct {
            Used  int64 `json:"used"`
            Total int64 `json:"total"`
        } `json:"data"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    
    if !result.Success {
        return nil, fmt.Errorf("API returned success=false")
    }
    
    return &CapacityInfo{
        Used:  result.Data.Used,
        Total: result.Data.Total,
    }, nil
}

// 移动端API（带参数）
func (d *Quark) tryMobileAPIWithParams() (*CapacityInfo, error) {
    // 从Cookie中提取认证参数
    authParams := d.extractAuthParams()
    
    // 构建请求URL
    url := "https://drive-m.quark.cn/1/clouddrive/capacity/growth/info"
    if len(authParams) > 0 {
        params := make([]string, 0, len(authParams))
        for k, v := range authParams {
            params = append(params, fmt.Sprintf("%s=%s", k, v))
        }
        url += "?" + strings.Join(params, "&")
    }
    
    client := &http.Client{Timeout: 10 * time.Second}
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("Cookie", d.Cookie)
    req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)")
    
    resp, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    // 处理响应...
    return nil, fmt.Errorf("mobile API not implemented yet")
}

// 从文件计算容量
func (d *Quark) estimateFromFiles() (*CapacityInfo, error) {
    totalUsed, err := d.calculateDirectorySize("/")
    if err != nil {
        return nil, err
    }
    
    return &CapacityInfo{
        Used:  totalUsed,
        Total: 10 * 1024 * 1024 * 1024 * 1024, // 10TB
    }, nil
}

// 递归计算目录大小
func (d *Quark) calculateDirectorySize(path string) (int64, error) {
    files, err := d.List(context.Background(), path, model.ListArgs{})
    if err != nil {
        return 0, err
    }
    
    var totalSize int64
    for _, file := range files {
        if file.IsDir() {
            subSize, err := d.calculateDirectorySize(file.GetPath())
            if err != nil {
                continue // 忽略错误，继续计算
            }
            totalSize += subSize
        } else {
            totalSize += file.GetSize()
        }
    }
    
    return totalSize, nil
}

// 提取认证参数
func (d *Quark) extractAuthParams() map[string]string {
    params := make(map[string]string)
    cookies := strings.Split(d.Cookie, ";")
    
    for _, cookie := range cookies {
        parts := strings.SplitN(strings.TrimSpace(cookie), "=", 2)
        if len(parts) != 2 {
            continue
        }
        
        key := strings.TrimSpace(parts[0])
        value := strings.TrimSpace(parts[1])
        
        // 查找可能的认证参数
        authKeys := []string{"sign", "token", "vcode", "timestamp", "nonce"}
        for _, authKey := range authKeys {
            if strings.Contains(strings.ToLower(key), authKey) {
                params[key] = value
                break
            }
        }
    }
    
    return params
}
```

### 2. 配置选项
```go
// 在驱动配置中添加容量获取选项
type Config struct {
    Cookie              string `json:"cookie" required:"true"`
    OrderBy             string `json:"order_by" type:"select" options:"file_type,file_name,updated_at" default:"file_name"`
    OrderDirection      string `json:"order_direction" type:"select" options:"asc,desc" default:"asc"`
    EnableCapacityCheck bool   `json:"enable_capacity_check" default:"true" help:"是否启用容量检查"`
    CapacityMethod      string `json:"capacity_method" type:"select" options:"auto,web,mobile,estimate" default:"auto" help:"容量获取方法"`
}
```

### 3. 错误处理和日志
```go
func (d *Quark) GetCapacity() (*CapacityInfo, error) {
    log.Debugf("[quark] 开始获取容量信息，方法: %s", d.CapacityMethod)
    
    var lastErr error
    
    switch d.CapacityMethod {
    case "web":
        capacity, err := d.tryWebAPI()
        if err == nil {
            log.Infof("[quark] 网页端API获取容量成功: %d/%d", capacity.Used, capacity.Total)
            return capacity, nil
        }
        lastErr = err
        
    case "mobile":
        capacity, err := d.tryMobileAPIWithParams()
        if err == nil {
            log.Infof("[quark] 移动端API获取容量成功: %d/%d", capacity.Used, capacity.Total)
            return capacity, nil
        }
        lastErr = err
        
    case "estimate":
        capacity, err := d.estimateFromFiles()
        if err == nil {
            log.Infof("[quark] 文件估算容量成功: %d/%d", capacity.Used, capacity.Total)
            return capacity, nil
        }
        lastErr = err
        
    default: // auto
        // 按优先级尝试所有方法
        methods := []func() (*CapacityInfo, error){
            d.tryWebAPI,
            d.tryMobileAPIWithParams,
            d.estimateFromFiles,
        }
        
        for i, method := range methods {
            if capacity, err := method(); err == nil {
                log.Infof("[quark] 方法%d获取容量成功: %d/%d", i+1, capacity.Used, capacity.Total)
                return capacity, nil
            } else {
                log.Debugf("[quark] 方法%d失败: %v", i+1, err)
                lastErr = err
            }
        }
    }
    
    log.Warnf("[quark] 所有容量获取方法都失败，返回默认值。最后错误: %v", lastErr)
    
    // 返回默认值，不影响其他功能
    return &CapacityInfo{
        Used:  0,
        Total: 0,
    }, nil
}
```

## 使用建议

1. **立即实施**: 使用方案4（返回默认值），确保功能稳定
2. **逐步优化**: 实施网页端API分析，寻找正确的端点
3. **用户反馈**: 收集用户的Cookie和网络请求信息，帮助调试
4. **持续监控**: 定期检查夸克网盘API的变化

## 测试方法

1. 在浏览器中登录夸克网盘
2. 使用开发者工具监控网络请求
3. 查找容量相关的API调用
4. 复制请求头和参数到代码中测试

---
*创建时间: 2025-08-25*
*状态: 待实施*

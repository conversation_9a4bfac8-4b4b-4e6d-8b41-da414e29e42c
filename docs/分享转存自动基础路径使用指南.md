# 分享转存自动基础路径功能使用指南

## 📋 功能概述

AList现已支持分享转存自动基础路径功能，当用户转存不同网盘的分享链接时，系统会自动为用户创建对应的分享基础路径储存空间，实现智能分类管理。

## ✨ 新功能特性

### 🎯 自动化管理
- **自动创建分享文件夹**：在对应网盘驱动器根目录下自动创建`<用户名>分享`文件夹
- **自动添加基础路径**：为用户自动添加分享类型基础路径储存空间
- **智能命名**：基础路径以网盘名称命名（百度分享、阿里分享等）
- **容量控制**：每个分享基础路径默认50GB容量限制

### 🏷️ 分享基础路径命名
- 百度网盘分享 → `百度分享`
- 阿里网盘分享 → `阿里分享`
- 123网盘分享 → `123分享`
- 夸克网盘分享 → `夸克分享`
- 迅雷网盘分享 → `迅雷分享`
- 其他网盘分享 → `其他分享`

## 🔧 使用步骤

### 第一步：准备工作
1. **确保系统配置**：管理员需要在AList中配置对应的网盘驱动器
2. **用户权限**：确保用户具有写入权限
3. **获取Cookie**：按照原有方式获取百度网盘Cookie

### 第二步：执行转存
1. **登录AList**：使用您的用户账号登录
2. **打开分享转存**：点击右下角悬浮按钮中的"分享转存"
3. **输入信息**：
   - 输入百度网盘Cookie
   - 输入分享链接（支持多种网盘类型）
4. **开始转存**：点击"开始转存"按钮

### 第三步：查看结果
1. **自动创建**：系统会自动为您创建对应的分享基础路径
2. **文件转存**：文件直接转存到用户分享文件夹中（如：`8844分享`）
3. **自动刷新**：转存成功后页面会自动刷新显示新文件
4. **访问文件**：在左侧存储空间列表中可以看到新创建的分享基础路径

## 📁 文件组织结构

### 系统层面
```
百度网盘驱动器/
├── user1分享/           # 用户1的分享文件夹（直接转存到这里）
│   ├── 转存的电影.mp4
│   ├── 转存的文档.pdf
│   └── 转存的文件夹/
│       ├── 子文件1.txt
│       └── 子文件2.jpg
└── user2分享/           # 用户2的分享文件夹
    ├── 转存的音乐.mp3
    └── 转存的图片.png

阿里网盘驱动器/
├── user1分享/           # 用户1的分享文件夹
└── user2分享/           # 用户2的分享文件夹
```

### 用户界面
```
我的存储空间：
├── 储存空间1           # 原有的储存空间
├── 百度分享            # 自动创建的百度分享基础路径
├── 阿里分享            # 自动创建的阿里分享基础路径
└── 123分享             # 自动创建的123分享基础路径
```

## 🎯 使用场景

### 场景1：单一网盘转存
**操作**：转存百度网盘分享链接
**结果**：
- 自动在百度网盘驱动器根目录创建`<用户名>分享`文件夹
- 为用户添加`百度分享`基础路径（50GB容量限制）
- 文件转存到`百度分享`基础路径中

### 场景2：多网盘混合转存
**操作**：同时转存百度网盘和阿里网盘分享链接
**结果**：
- 自动创建对应的用户分享文件夹
- 为用户添加`百度分享`和`阿里分享`基础路径
- 文件按类型分别转存到对应的基础路径

### 场景3：重复转存
**操作**：再次转存相同类型的分享链接
**结果**：
- 检测到已存在对应的分享基础路径
- 直接转存到现有的基础路径中
- 不会重复创建基础路径

## 💡 使用技巧

### 🔍 查看分享基础路径
1. **左侧导航**：在左侧存储空间列表中查看所有分享基础路径
2. **容量信息**：每个分享基础路径显示独立的容量使用情况
3. **文件访问**：点击分享基础路径可直接访问转存的文件

### 📊 容量管理
1. **独立限制**：每个分享基础路径有独立的50GB容量限制
2. **实时监控**：系统实时监控各分享基础路径的容量使用
3. **超限提醒**：接近容量限制时会有相应提醒

### 🔄 文件管理
1. **分类清晰**：不同网盘的文件自动分类，便于管理
2. **用户隔离**：每个用户的分享文件完全隔离
3. **权限控制**：遵循原有的用户权限体系

## ⚠️ 注意事项

### 系统要求
1. **驱动器配置**：管理员需要预先配置对应的网盘驱动器
2. **用户权限**：用户需要具有写入权限才能使用转存功能
3. **存储空间**：确保对应的网盘驱动器有足够的存储空间

### 使用限制
1. **容量限制**：每个分享基础路径默认50GB容量限制
2. **转存限制**：仍然遵循原有的转存限制（单次最多10个链接等）
3. **网盘支持**：目前主要支持百度网盘转存，其他网盘主要用于分类

### 安全提醒
1. **Cookie安全**：请妥善保管您的网盘Cookie
2. **合规使用**：仅用于个人合法文件的转存
3. **定期清理**：建议定期清理不需要的转存文件

## 🆕 与原功能的区别

### 原功能
- 转存到固定的分享文件夹
- 用户需要手动管理文件分类
- 所有类型文件混合存储

### 新功能
- 自动创建分享基础路径
- 智能分类不同网盘的文件
- 独立的容量管理和权限控制
- 更直观的用户界面显示

## 🔧 故障排除

### 常见问题
1. **分享基础路径未创建**：检查用户权限和驱动器配置
2. **转存失败**：检查Cookie有效性和网络连接
3. **文件找不到**：确认在正确的分享基础路径中查找

### 解决方案
1. **权限问题**：联系管理员检查用户权限设置
2. **驱动器问题**：联系管理员检查网盘驱动器配置
3. **容量问题**：清理不需要的文件或联系管理员调整容量限制

---

**功能版本**: v2.0.0
**更新日期**: 2024-12-24
**兼容性**: 向后兼容，不影响现有功能

# 基础路径立即自动清理功能说明

## 功能概述

当管理员在后台管理界面删除用户的基础路径储存空间时，系统现在会**立即自动**删除对应的文件夹，无需额外运行清理脚本或手动操作。

## 工作流程

1. **用户更新操作**：管理员在后台编辑用户，删除某个基础路径储存空间
2. **自动检测**：系统自动检测被删除的基础路径
3. **立即清理**：系统立即在后台异步删除对应的文件夹
4. **操作完成**：用户更新操作正常完成，文件夹清理在后台进行

## 技术实现

### 核心组件
- **数据库层**：`internal/db/user.go` - 检测被删除的基础路径并触发清理
- **处理层**：`server/handles/cleanup.go` - 实现实际的文件夹删除逻辑
- **文件系统层**：`internal/fs` - 提供跨存储驱动的文件操作接口

### 清理机制
- **异步处理**：清理操作在后台goroutine中执行，不阻塞用户更新
- **安全检查**：拒绝删除根目录，确保系统安全
- **超时控制**：每个清理操作有30秒超时限制
- **错误隔离**：清理失败不影响用户更新操作

## 使用方法

### 自动清理（默认行为）

**无需任何额外操作！** 当您删除用户的基础路径储存空间时，系统会自动清理对应的文件夹。

### 手动API调用（可选）

```bash
# 单个文件夹清理
curl -X POST "http://localhost:5244/api/admin/cleanup/base_path_folder?path=/path/to/delete" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 批量文件夹清理
curl -X POST "http://localhost:5244/api/admin/cleanup/batch_base_path_folders" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"paths": ["/path1", "/path2"]}'
```

## 日志和监控

### 日志关键字
在AList主程序日志中，您可以搜索以下关键字来监控清理操作：

- `"Starting async cleanup for base path"` - 开始清理操作
- `"Async cleanup completed for base path"` - 清理成功完成
- `"Async cleanup failed for base path"` - 清理失败
- `"Refusing to delete root path"` - 安全检查拒绝删除根目录

### 监控示例

```bash
# 查看清理相关的日志
tail -f logs/alist.log | grep -i cleanup

# 查看最近的清理操作
grep "cleanup" logs/alist.log | tail -20
```

## 安全注意事项

1. **路径验证**：系统会拒绝删除根目录（"/"）
2. **权限检查**：清理API需要管理员权限
3. **超时控制**：每个清理操作有30秒超时限制
4. **错误处理**：清理失败不会影响用户更新操作

## 故障排除

### 常见问题

1. **清理操作没有执行**
   - 检查AList服务日志，确认是否有清理相关的日志
   - 确认删除的是基础路径而不是根目录

2. **清理操作失败**
   - 检查存储驱动是否正常工作
   - 检查文件夹权限和网络连接
   - 查看详细错误日志

3. **清理操作超时**
   - 大文件夹可能需要更长时间，这是正常现象
   - 清理操作有30秒超时限制，超时后会记录错误但不影响系统

### 手动清理

如果需要手动清理特定路径：

```bash
# 手动调用清理API
curl -X POST "http://localhost:5244/api/admin/cleanup/base_path_folder?path=/your/path" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 配置选项

目前清理功能的配置是硬编码的，包括：

- **清理超时时间**：30秒
- **清理方式**：异步后台执行
- **安全检查**：拒绝删除根目录
- **错误处理**：清理失败不影响用户更新

未来版本可能会添加以下配置选项：
- 可配置的清理超时时间
- 清理策略选择（立即/延迟/禁用）
- 清理日志级别控制

## 版本历史

- **v1.0**：基础的队列机制和手动清理脚本
- **v2.0**：立即自动清理，无需额外脚本或手动操作
- **未来版本**：计划添加可配置选项、Web界面管理等功能

## 优势特点

1. **真正自动化**：删除基础路径后立即自动清理，无需人工干预
2. **非阻塞设计**：清理操作在后台执行，不影响用户操作响应速度
3. **安全可靠**：多重安全检查，防止误删重要数据
4. **跨存储支持**：支持所有AList支持的存储驱动类型
5. **错误隔离**：清理失败不会影响用户管理操作
6. **详细日志**：完整的操作日志便于问题排查和监控

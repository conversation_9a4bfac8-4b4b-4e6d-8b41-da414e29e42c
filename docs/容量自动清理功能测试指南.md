# 容量自动清理功能测试指南

## 🎯 测试目标

验证分享转存容量自动清理功能是否正常工作，确保当容量超过100%时能自动删除最新转存的文件。

## 📋 测试前准备

### 1. 重启后端服务
```bash
# 停止当前服务
pkill alist

# 启动新服务
./alist server
```

### 2. 检查容量配置
确保目标存储空间已启用容量限制：
- 存储空间：`百度分享` 或 `/baidu_jaques520/8844分享`
- 容量限制：10 GB
- 当前使用：约20.2 GB（已超限）

## 🧪 测试步骤

### 步骤1：准备测试文件
选择一些较大的分享链接进行转存测试，建议：
- 文件大小：100MB - 1GB
- 文件数量：2-3个
- 确保转存后会进一步超出容量限制

### 步骤2：执行转存
1. 访问AList前端界面
2. 进入分享转存功能
3. 输入测试的分享链接
4. 开始转存

### 步骤3：观察日志
在转存过程中和完成后，观察后端日志中是否出现以下关键信息：

#### 转存开始阶段
```
INFO 后台容量清理管理器已启动，用户: 8844, 目标路径: /baidu_jaques520/8844分享, 容量限制: 10.00 GB
INFO 已启用容量控制，容量限制: 10.00 GB
```

#### 转存完成阶段
```
INFO 🚀 转存完成，启动智能容量监控和清理任务...
INFO 📊 开始执行转存后的容量检查...
```

#### 容量检查阶段
```
INFO 容量检查 - 当前使用率: XXX.X% (XX.X GB / 10.0 GB), 记录文件数: X
INFO 🚨 检测到容量超限(XXX.X%%)，开始自动清理转存文件
```

#### 文件清理阶段
```
INFO 🧹 开始清理转存文件，当前记录数: X
INFO 📁 记录文件[1]: 文件名.xxx (XXX.X MB, 顺序: X)
INFO 🗑️ 正在删除文件: 文件名.xxx (大小: XXX.X MB, 转存顺序: X, 路径: /path/to/file)
INFO 已删除文件: 文件名.xxx (大小: XXX.X MB)
INFO 删除后容量使用率: XXX.X%%
```

#### 清理完成阶段
```
INFO ✅ 容量已降至XX.X%，清理任务完成
INFO 🏁 智能清理任务完成，停止后台清理管理器
```

## 🔍 故障排查

### 如果没有看到预期日志

#### 1. 检查容量限制配置
```bash
# 查看用户配置
grep -A 10 -B 10 "8844分享" data/config.json
```

#### 2. 检查日志级别
确保日志级别设置为INFO或DEBUG：
```bash
# 查看当前日志配置
grep -i "log" data/config.json
```

#### 3. 手动触发容量检查
```bash
# 查看当前容量使用情况
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:5244/api/me?refresh_capacity=true"
```

### 常见问题

#### 问题1：没有启动后台清理管理器
**可能原因**：
- 容量限制未启用
- 目标基础路径配置错误

**解决方案**：
- 检查基础路径配置
- 确保EnableCapacityLimit=true

#### 问题2：记录的文件数为0
**可能原因**：
- 转存文件记录失败
- getBasePathID函数返回错误

**解决方案**：
- 检查转存成功的日志
- 验证文件路径是否正确

#### 问题3：容量检查显示正常
**可能原因**：
- 容量计算延迟
- 缓存未更新

**解决方案**：
- 等待容量重新计算完成
- 手动刷新容量信息

## 📊 预期结果

### 成功场景
1. **转存成功**：文件正常转存到目标目录
2. **容量检查**：检测到容量超过100%
3. **自动清理**：按转存顺序倒序删除文件
4. **停止条件**：容量降到100%以下时停止删除
5. **日志完整**：所有关键步骤都有详细日志

### 失败场景处理
如果功能仍不工作，请提供：
1. 完整的转存日志
2. 容量配置信息
3. 转存的文件信息
4. 错误信息（如有）

## 🔧 调试命令

### 查看最近的转存日志
```bash
tail -100 data/log/log.log | grep -E "(转存|容量|清理|删除|后台)"
```

### 查看容量信息
```bash
tail -50 data/log/log.log | grep -E "(容量|capacity)"
```

### 查看用户配置
```bash
grep -A 20 "8844" data/config.json
```

## 📝 测试报告模板

```
测试时间：YYYY-MM-DD HH:MM:SS
测试文件：[文件名列表]
转存结果：成功/失败
容量检查：是/否
自动清理：是/否
删除文件数：X个
最终容量：XX.X%
问题描述：[如有问题请详细描述]
```

---

**注意**：测试过程中请保持耐心，容量计算和文件删除可能需要一些时间来同步。

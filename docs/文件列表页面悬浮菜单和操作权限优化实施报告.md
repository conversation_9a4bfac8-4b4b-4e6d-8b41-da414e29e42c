# 文件列表页面悬浮菜单和操作权限优化实施报告

## 概述

本次优化主要针对AList项目中"我的文件"和"分享转存"文件列表页面的用户体验进行改进，包括隐藏不必要的悬浮菜单、禁止根目录的某些操作，以及限制移动文件时的路径选择范围。

## 修改内容

### 1. 隐藏悬浮菜单

#### 1.1 隐藏底部悬浮菜单 (Center.tsx)
- **文件**: `alist-web/src/pages/home/<USER>/Center.tsx`
- **修改内容**:
  - 添加了路径判断逻辑 `shouldHideMenu()`
  - 在整个"分享转存"页面（`/shared`及所有子目录）隐藏底部悬浮菜单
  - 对多基础路径用户在整个"我的文件"页面（根路径及所有子目录）隐藏悬浮菜单
  - 对单基础路径用户在其基础路径范围内也隐藏悬浮菜单

#### 1.2 隐藏右侧悬浮菜单按钮 (Right.tsx)
- **文件**: `alist-web/src/pages/home/<USER>/Right.tsx`
- **修改内容**:
  - 添加了路径判断逻辑 `shouldHideMenu()`
  - 使用 `<Show when={!shouldHideMenu()}>` 包装整个右侧悬浮菜单
  - 在整个"分享转存"页面和"我的文件"页面完全隐藏右侧悬浮菜单按钮

### 2. 禁止根目录操作

#### 2.1 禁止新建文件夹 (Mkdir.tsx)
- **文件**: `alist-web/src/pages/home/<USER>/Mkdir.tsx`
- **修改内容**:
  - 添加 `shouldDisableMkdir()` 判断逻辑
  - 在根目录禁止新建文件夹操作
  - 显示友好的错误提示："当前目录不支持创建文件夹！"

#### 2.2 禁止删除操作 (Delete.tsx)
- **文件**: `alist-web/src/pages/home/<USER>/Delete.tsx`
- **修改内容**:
  - 添加 `shouldDisableDelete()` 判断逻辑
  - 在根目录禁止删除操作
  - 显示友好的错误提示："当前目录不支持删除操作！"

#### 2.3 禁止移动储存空间 (CopyMove.tsx)
- **文件**: `alist-web/src/pages/home/<USER>/CopyMove.tsx`
- **修改内容**:
  - 添加 `shouldDisableMove()` 判断逻辑
  - 在根目录禁止移动储存空间
  - 优化 `getRootPath()` 函数，限制移动路径范围在当前储存空间内
  - 显示友好的错误提示："当前目录不支持移动操作！"

#### 2.4 禁止上传文件但保持按钮可见 (Upload.tsx)
- **文件**: `alist-web/src/pages/home/<USER>/Upload.tsx`
- **修改内容**:
  - 修改 `shouldDisableUpload()` 判断逻辑（原为`isRootForNormalUser`）
  - 在"我的文件"根目录禁止上传文件
  - 对多基础路径用户在根路径也禁止上传
  - 上传按钮保持可见，但点击后会显示错误提示："当前目录不支持上传文件！"
  - 拖拽上传也会被同样的逻辑阻止

### 3. 优化路径选择范围

#### 3.1 优化文件夹选择器根路径 (path.ts)
- **文件**: `alist-web/src/utils/path.ts`
- **修改内容**:
  - 增强 `getFolderTreeRootPath()` 函数
  - 添加对分享转存储存空间路径的支持
  - 确保在储存空间内部移动文件时，路径选择范围限制在当前储存空间内

### 4. 工具导航栏按钮状态优化

#### 4.1 禁用工具导航栏按钮 (ToolNavbar.tsx)
- **文件**: `alist-web/src/pages/home/<USER>
- **修改内容**:
  - 添加 `shouldDisableRootOperations()` 判断逻辑
  - 在根目录禁用新建文件夹、移动、删除按钮
  - 调整按钮颜色和禁用状态，提供视觉反馈

## 判断逻辑

悬浮菜单隐藏基于以下判断逻辑：

```javascript
const shouldHideMenu = createMemo(() => {
  const user = me()
  const currentPath = pathname()

  // 在整个"分享转存"页面（包括所有子目录）隐藏悬浮菜单
  if (currentPath === "/shared" || currentPath.startsWith("/shared/")) {
    return true
  }

  // 对于多基础路径用户，在整个"我的文件"页面（根路径及所有子目录）隐藏悬浮菜单
  if (UserMethods.has_multiple_base_paths(user)) {
    return true
  }

  // 对于单基础路径用户，在其基础路径范围内也隐藏悬浮菜单
  const basePaths = UserMethods.get_base_paths(user)
  if (basePaths.length === 1) {
    const basePath = basePaths[0].path
    if (currentPath === basePath || currentPath.startsWith(basePath + "/")) {
      return true
    }
  }

  return false
})
```

根目录操作禁用基于以下判断逻辑：

```javascript
const shouldDisableRootOperations = createMemo(() => {
  const user = me()
  const path = pathname()

  // 在"分享转存"根目录禁止操作
  if (path === "/shared" || path === "/shared/") {
    return true
  }

  // 对于多基础路径用户，在根路径也禁止操作
  if (UserMethods.has_multiple_base_paths(user) && (path === "/" || path === "")) {
    return true
  }

  return false
})
```

**注意**: 上传功能的判断逻辑略有不同，主要针对"我的文件"根目录：

```javascript
const shouldDisableUpload = createMemo(() => {
  const user = me()
  const path = pathname()

  // 对于多基础路径用户，在根路径禁止上传
  if (UserMethods.has_multiple_base_paths(user) && (path === "/" || path === "")) {
    return true
  }

  // 对于普通用户在根目录也禁止上传
  if (!UserMethods.is_admin(user) && (path === "/" || path === "")) {
    return true
  }

  return false
})
```

## 用户体验改进

1. **界面简洁**: 在整个"我的文件"和"分享转存"页面隐藏不必要的悬浮菜单，减少界面干扰
2. **操作安全**: 防止用户误操作储存空间（删除、移动、上传等）
3. **路径限制**: 移动文件时只能在当前储存空间范围内选择目标路径
4. **友好提示**: 提供清晰的错误提示信息，告知用户操作限制原因
5. **按钮可见性**: 上传按钮保持可见，但功能受限，提供更好的用户反馈
6. **全面覆盖**: 不仅在根目录，整个文件列表页面都享受简洁的界面体验

## 兼容性

- 保持对管理员用户的完整功能支持
- 对单基础路径用户的现有行为保持兼容
- 多基础路径用户在储存空间内部的正常操作不受影响

## 测试建议

1. 测试多基础路径用户在根目录的操作限制
2. 测试在储存空间内部的正常文件操作
3. 测试移动文件时的路径选择范围限制
4. 测试悬浮菜单的显示/隐藏逻辑
5. 测试管理员用户的完整功能访问权限
6. 测试上传按钮的可见性和功能限制
7. 测试拖拽上传在根目录的限制行为

## 总结

本次优化成功实现了以下目标：
- ✅ 隐藏整个"我的文件"和"分享转存"页面的悬浮菜单
- ✅ 禁止根目录的新建文件夹和删除操作
- ✅ 禁止根目录储存空间的移动操作
- ✅ 禁止"我的文件"根目录的上传操作，但保持上传按钮可见
- ✅ 限制移动文件的路径选择范围在当前储存空间内
- ✅ 提供友好的用户提示信息
- ✅ 保持代码的一致性和可维护性
- ✅ 全面覆盖所有文件列表页面，不仅限于根目录

所有修改都遵循了项目的代码规范，并确保了向后兼容性。

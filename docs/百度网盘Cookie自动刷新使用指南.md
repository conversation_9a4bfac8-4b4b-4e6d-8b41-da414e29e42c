# 百度网盘Cookie自动刷新功能使用指南

## 功能简介

百度网盘Cookie自动刷新功能可以自动维护百度网盘存储的Cookie有效性，确保分享转存功能持续可用，无需手动更新Cookie。

## 主要特性

- 🔄 **自动检测**：每2小时自动检查Cookie有效性
- 🚀 **智能刷新**：检测到失效时自动获取新Cookie
- 📊 **状态监控**：提供详细的Cookie状态查询
- 🛡️ **故障恢复**：分享转存时自动处理Cookie失效

## 使用方法

### 1. 系统要求

- AList v3.x 版本
- 已配置百度网盘存储
- 管理员权限（用于状态查询和手动控制）

### 2. 自动启动

Cookie管理器会在AList启动时自动启动，无需手动配置。启动后会：

- 立即检查所有百度网盘存储的Cookie状态
- 启动定时任务，每2小时检查一次
- 在分享转存时自动验证和刷新Cookie

### 3. 管理员操作

#### 查看Cookie状态

```bash
curl -H "Authorization: YOUR_ADMIN_TOKEN" \
     http://localhost:5244/api/admin/cookie/status
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "baidu_netdisk": [
      {
        "is_valid": true,
        "last_checked": "2024-08-18T16:08:41+08:00",
        "storage_id": 1
      }
    ]
  }
}
```

#### 手动刷新Cookie

```bash
curl -X POST -H "Authorization: YOUR_ADMIN_TOKEN" \
     http://localhost:5244/api/admin/cookie/refresh/1
```

#### 查看管理器状态

```bash
curl -H "Authorization: YOUR_ADMIN_TOKEN" \
     http://localhost:5244/api/admin/cookie/manager/status
```

### 4. 分享转存使用

分享转存功能现在会自动处理Cookie失效问题：

1. **正常使用**：直接使用分享转存功能，无需关心Cookie状态
2. **自动处理**：如果Cookie失效，系统会自动尝试刷新
3. **透明体验**：整个过程对用户透明，无需手动干预

## 监控和维护

### 日志监控

Cookie管理器会输出详细的日志信息：

```
INFO[时间] 启动百度网盘Cookie自动刷新管理器
INFO[时间] 开始检查百度网盘Cookie状态...
INFO[时间] 检查存储 /baidu_xxx (ID: 1) 的Cookie状态
INFO[时间] 存储 /baidu_xxx 的Cookie有效
```

如果发现Cookie失效：
```
WARN[时间] 存储 /baidu_xxx 的Cookie已失效，尝试刷新...
INFO[时间] 成功刷新存储 /baidu_xxx 的Cookie
```

### 状态检查

定期检查Cookie状态，确保系统正常运行：

```bash
# 使用测试脚本
python3 test_cookie_auto_refresh.py

# 或直接调用API
curl -H "Authorization: YOUR_TOKEN" \
     http://localhost:5244/api/admin/cookie/status
```

## 故障排除

### 常见问题

1. **Cookie刷新失败**
   - 检查原始Cookie中的BDUSS是否有效
   - 确认网络连接正常
   - 查看服务器日志获取详细错误信息

2. **管理器未启动**
   - 检查服务器启动日志
   - 确认编译时包含了Cookie管理器代码
   - 尝试手动启动：`POST /api/admin/cookie/manager/start`

3. **API访问被拒绝**
   - 确认使用管理员账号
   - 检查Authorization头是否正确
   - 验证token是否有效

### 调试步骤

1. **检查服务状态**
   ```bash
   curl http://localhost:5244/api/public/settings
   ```

2. **验证管理员权限**
   ```bash
   curl -H "Authorization: YOUR_TOKEN" \
        http://localhost:5244/api/me
   ```

3. **查看详细日志**
   - 启用DEBUG级别日志
   - 观察Cookie检查和刷新过程
   - 记录错误信息用于分析

## 配置优化

### 调整检查间隔

默认每2小时检查一次，可以根据需要调整：

```go
// 在 internal/cookie_manager/baidu_cookie_manager.go 中
refreshInterval: 1 * time.Hour, // 改为1小时
```

### 网络超时设置

```go
// HTTP客户端超时设置
client: &http.Client{
    Timeout: 60 * time.Second, // 增加到60秒
}
```

## 安全注意事项

1. **Cookie保护**
   - Cookie信息存储在数据库中，确保数据库安全
   - 日志中的敏感信息已脱敏处理
   - 网络传输建议使用HTTPS

2. **访问控制**
   - Cookie管理API仅限管理员访问
   - 定期更换管理员密码和token
   - 监控API访问日志

3. **备份恢复**
   - 定期备份存储配置
   - 记录有效的Cookie信息
   - 准备应急恢复方案

## 性能影响

### 资源使用

- **内存**：约5-10MB额外内存占用
- **CPU**：定时检查时短暂增加CPU使用
- **网络**：每次检查约1-2KB流量

### 优化建议

- 根据使用频率调整检查间隔
- 在低峰期执行Cookie刷新
- 监控系统资源使用情况

## 更新和维护

### 版本兼容性

- 兼容AList v3.x所有版本
- 向后兼容现有存储配置
- 不影响其他功能正常使用

### 功能扩展

未来可能的扩展功能：
- 支持更多网盘平台
- 智能预测Cookie失效时间
- 集群环境下的Cookie同步
- 监控告警和通知功能

## 技术支持

如果遇到问题：

1. **查看文档**：阅读详细的实施报告
2. **检查日志**：查看服务器日志获取错误信息
3. **运行测试**：使用提供的测试脚本验证功能
4. **社区支持**：在AList社区寻求帮助

## 总结

百度网盘Cookie自动刷新功能大大提高了分享转存功能的稳定性和可靠性。通过自动化的Cookie维护，用户可以专注于文件管理，而无需担心Cookie失效问题。

主要优势：
- ✅ **自动化**：无需人工干预
- ✅ **可靠性**：多重保障机制
- ✅ **透明性**：对用户完全透明
- ✅ **可管理**：提供完整的管理接口

建议定期检查Cookie状态，确保系统持续稳定运行。

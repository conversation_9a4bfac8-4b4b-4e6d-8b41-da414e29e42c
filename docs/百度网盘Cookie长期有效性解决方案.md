# 百度网盘Cookie长期有效性解决方案

## 问题背景

百度网盘的Cookie会定期过期，主要包含以下字段：
- `BDUSS`: 用户身份标识，有效期通常为几个月
- `STOKEN`: 会话令牌，有效期较短
- `BAIDUID`: 设备标识
- `PANWEB`: 网页版标识

当Cookie过期时，分享转存功能会失败，需要手动更新Cookie。

## 当前实现的解决方案

### 1. Cookie有效性验证
已在`server/handles/fsmanage.go`中实现：

```go
// isValidBaiduCookie 验证百度网盘Cookie是否有效
func isValidBaiduCookie(cookie string) bool {
    // 基本格式检查
    if !strings.Contains(cookie, "BAIDUID") || !strings.Contains(cookie, "BDUSS") {
        return false
    }
    
    // API验证：发送请求到百度网盘配额接口
    client := createBaiduHttpClient()
    req, err := http.NewRequest("GET", "https://pan.baidu.com/api/quota", nil)
    req.Header.Set("Cookie", cookie)
    
    resp, err := client.Do(req)
    // 如果返回401或403，说明Cookie无效
    if resp.StatusCode == 401 || resp.StatusCode == 403 {
        return false
    }
    
    return true
}
```

### 2. 智能Cookie获取
在`getBaiduNetdiskCookie()`函数中：
- 自动验证Cookie有效性
- 记录过期警告日志
- 只返回有效的Cookie

## 进一步的解决方案

### 方案一：多Cookie轮换机制

#### 实现思路
1. 支持配置多个百度网盘存储，每个配置不同的Cookie
2. 当一个Cookie失效时，自动切换到下一个有效的Cookie
3. 实现Cookie池管理

#### 配置方式
```json
{
  "cookie_pool": [
    "BAIDUID=xxx; BDUSS=yyy; STOKEN=zzz",
    "BAIDUID=aaa; BDUSS=bbb; STOKEN=ccc",
    "BAIDUID=ddd; BDUSS=eee; STOKEN=fff"
  ]
}
```

### 方案二：Cookie自动刷新机制

#### 实现思路
1. 定期检测Cookie有效性（每小时或每天）
2. 当检测到Cookie即将过期时，尝试自动刷新
3. 使用百度网盘的刷新Token机制

#### 技术实现
```go
// 定期任务检查Cookie有效性
func startCookieValidationTask() {
    ticker := time.NewTicker(1 * time.Hour)
    go func() {
        for range ticker.C {
            validateAndRefreshCookies()
        }
    }()
}

func validateAndRefreshCookies() {
    storages, _ := db.GetEnabledStorages()
    for _, storage := range storages {
        if storage.Driver == "BaiduNetdisk" {
            // 检查并刷新Cookie
            refreshCookieIfNeeded(storage)
        }
    }
}
```

### 方案三：通知机制

#### 实现思路
1. 当Cookie即将过期时，发送通知给管理员
2. 支持多种通知方式：邮件、WebSocket、系统通知
3. 提供Cookie更新的便捷入口

#### 通知内容
```
标题：百度网盘Cookie即将过期
内容：存储 "百度网盘-主账号" 的Cookie将在3天后过期，请及时更新。
操作：[立即更新] [延后提醒]
```

## 推荐的最佳实践

### 1. 短期解决方案（已实现）
- ✅ Cookie有效性验证
- ✅ 过期警告日志
- ✅ 智能Cookie选择

### 2. 中期解决方案
- 🔄 多Cookie轮换机制
- 🔄 定期有效性检查
- 🔄 WebSocket通知机制

### 3. 长期解决方案
- 🔄 Cookie自动刷新
- 🔄 邮件通知系统
- 🔄 管理界面优化

## Cookie获取和更新指南

### 获取Cookie的步骤
1. 登录百度网盘网页版
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面
5. 找到任意请求，复制完整的Cookie

### Cookie格式示例
```
BAIDUID=xxx; BDUSS=yyy; STOKEN=zzz; PANWEB=1; ...
```

### 更新频率建议
- 主动监控：每周检查一次
- 被动更新：收到过期通知时立即更新
- 预防性更新：每月主动更新一次

## 监控和维护

### 日志监控
关注以下日志信息：
```
WARN: 百度网盘存储 /baidu_xxx 的Cookie可能已过期，请更新
ERROR: 未找到配置了有效Cookie的百度网盘存储
```

### 性能影响
- Cookie验证请求：每次转存前验证一次
- 定期检查：建议设置为每小时一次
- 网络开销：每次验证约1KB数据传输

## 安全考虑

1. **Cookie保护**：确保Cookie在数据库中加密存储
2. **访问控制**：只有管理员可以查看和修改Cookie
3. **日志脱敏**：日志中不记录完整Cookie内容
4. **传输安全**：使用HTTPS传输Cookie数据

## 总结

当前已实现基础的Cookie有效性验证机制，能够：
- 自动检测Cookie是否有效
- 记录过期警告
- 避免使用无效Cookie

建议后续实现多Cookie轮换和通知机制，以进一步提高系统的稳定性和用户体验。

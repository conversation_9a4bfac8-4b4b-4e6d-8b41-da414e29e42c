package bootstrap

import (
	"github.com/alist-org/alist/v3/internal/cookie_manager"
	log "github.com/sirupsen/logrus"
)

// InitCookieManager 初始化Cookie管理器
func InitCookieManager() {
	log.Info("初始化Cookie管理器...")
	
	// 启动Cookie管理器
	cookie_manager.StartCookieManager()
	
	log.Info("Cookie管理器初始化完成")
}

// StopCookieManager 停止Cookie管理器
func StopCookieManager() {
	log.Info("停止Cookie管理器...")
	cookie_manager.StopCookieManager()
	log.Info("Cookie管理器已停止")
}

package v3_42_0

import (
	"github.com/alist-org/alist/v3/internal/db"
	"github.com/alist-org/alist/v3/internal/model"
	"github.com/alist-org/alist/v3/pkg/utils"
)

// UpdateBasePathType sets default type for existing base paths
// This patch ensures all existing base paths have the "储存" type
func UpdateBasePathType() {
	// 获取所有基础路径记录
	var basePaths []model.UserBasePath
	if err := db.GetDb().Find(&basePaths).Error; err != nil {
		utils.Log.Errorf("Cannot get base paths for type update: %v", err)
		return
	}

	// 为没有设置类型的基础路径设置默认类型
	for i := range basePaths {
		basePath := &basePaths[i]
		if basePath.Type == "" {
			basePath.Type = "储存"
			if err := db.GetDb().Save(basePath).Error; err != nil {
				utils.Log.Errorf("Cannot update base path type for ID %d: %v", basePath.ID, err)
			} else {
				utils.Log.Infof("Updated base path type for ID %d to '储存'", basePath.ID)
			}
		}
	}
}

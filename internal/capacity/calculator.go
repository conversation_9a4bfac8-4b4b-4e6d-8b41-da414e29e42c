package capacity

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"

	"github.com/alist-org/alist/v3/internal/fs"
	"github.com/alist-org/alist/v3/internal/model"
	log "github.com/sirupsen/logrus"
)

// Calculator 容量计算引擎
type Calculator struct {
	config    *Config
	cache     *Cache
	persister *Persister
	
	// 并发控制
	semaphore chan struct{}
	
	// 统计信息
	stats *CalculatorStats
}

// CalculatorStats 计算器统计信息
type CalculatorStats struct {
	TotalCalculations    int64 // 总计算次数
	SuccessCalculations  int64 // 成功计算次数
	FailedCalculations   int64 // 失败计算次数
	TotalCalculationTime int64 // 总计算时间(毫秒)
	ActiveCalculations   int64 // 当前活跃计算数
}

// NewCalculator 创建新的容量计算器
func NewCalculator(config *Config, cache *Cache, persister *Persister) *Calculator {
	return &Calculator{
		config:    config,
		cache:     cache,
		persister: persister,
		semaphore: make(chan struct{}, config.MaxConcurrency),
		stats:     &CalculatorStats{},
	}
}

// Calculate 计算指定基础路径的容量
func (c *Calculator) Calculate(ctx context.Context, basePath string) (*model.BasePathCapacity, error) {
	// 获取信号量，控制并发
	select {
	case c.semaphore <- struct{}{}:
		defer func() { <-c.semaphore }()
	case <-ctx.Done():
		return nil, ctx.Err()
	}
	
	atomic.AddInt64(&c.stats.TotalCalculations, 1)
	atomic.AddInt64(&c.stats.ActiveCalculations, 1)
	defer atomic.AddInt64(&c.stats.ActiveCalculations, -1)
	
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		atomic.AddInt64(&c.stats.TotalCalculationTime, duration.Milliseconds())
	}()
	
	log.Debugf("Starting capacity calculation for path: %s", basePath)

	// 不设置超时，确保完全计算
	// 对于大容量存储，需要足够的时间来完成计算

	// 执行计算
	capacity, err := c.calculateCapacity(ctx, basePath)
	if err != nil {
		atomic.AddInt64(&c.stats.FailedCalculations, 1)
		log.Errorf("Failed to calculate capacity for path %s: %v", basePath, err)

		// 不保存错误状态到数据库，直接返回错误
		// 这样下次请求时会重新尝试计算，而不是返回错误的0值
		return nil, err
	}
	
	atomic.AddInt64(&c.stats.SuccessCalculations, 1)
	
	// 更新计算状态
	capacity.CalculationStatus = "completed"
	capacity.ErrorMessage = ""
	capacity.LastCalculatedAt = time.Now()
	
	// 保存到数据库
	if err := c.persister.Save(capacity); err != nil {
		log.Errorf("Failed to save capacity to database: %v", err)
		// 不返回错误，因为计算本身是成功的
	}
	
	// 更新缓存
	c.cache.SetToL1(basePath, capacity)
	c.cache.SetToL2(basePath, capacity)
	
	log.Debugf("Completed capacity calculation for path %s: %d bytes, %d files", 
		basePath, capacity.UsedBytes, capacity.TotalFiles)
	
	return capacity, nil
}

// calculateCapacity 执行实际的容量计算
func (c *Calculator) calculateCapacity(ctx context.Context, basePath string) (*model.BasePathCapacity, error) {
	var totalSize int64
	var totalFiles int64
	
	// 递归计算目录大小
	size, files, err := c.calculateDirSize(ctx, basePath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate directory size: %w", err)
	}
	
	totalSize += size
	totalFiles += files
	
	// 获取现有记录的版本号
	version := int64(1)
	if existing, err := c.persister.Get(basePath); err == nil && existing != nil {
		version = existing.Version + 1
	}
	
	capacity := &model.BasePathCapacity{
		BasePath:          basePath,
		UsedBytes:         totalSize,
		TotalFiles:        totalFiles,
		LastCalculatedAt:  time.Now(),
		Version:           version,
		CalculationStatus: "calculating",
		ErrorMessage:      "",
	}
	
	return capacity, nil
}

// calculateDirSize 递归计算目录大小，支持分批处理和进度报告
func (c *Calculator) calculateDirSize(ctx context.Context, dirPath string) (int64, int64, error) {
	// 检查上下文是否已取消
	select {
	case <-ctx.Done():
		return 0, 0, ctx.Err()
	default:
	}

	var totalSize int64
	var totalFiles int64

	// 获取目录列表，强制刷新缓存以获取最新文件
	objs, err := fs.List(ctx, dirPath, &fs.ListArgs{
		Refresh: true, // 强制刷新缓存，确保获取最新的文件列表
	})
	if err != nil {
		// 如果目录不存在或无法访问，返回0而不是错误
		// 这对于新创建的分享基础路径很重要
		log.Warnf("Failed to list directory %s (may not exist yet): %v", dirPath, err)
		return 0, 0, nil
	}

	log.Debugf("Found %d objects in directory %s (with cache refresh)", len(objs), dirPath)

	// 优化批处理，提高处理速度
	const batchSize = 200 // 增加批处理大小
	processedCount := 0

	for i := 0; i < len(objs); i += batchSize {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			log.Debugf("Context cancelled while processing directory %s, processed %d/%d items",
				dirPath, processedCount, len(objs))
			return totalSize, totalFiles, ctx.Err()
		default:
		}

		end := i + batchSize
		if end > len(objs) {
			end = len(objs)
		}

		batch := objs[i:end]
		log.Debugf("Processing batch %d-%d/%d in directory %s", i+1, end, len(objs), dirPath)

		for _, obj := range batch {
			if obj.IsDir() {
				// 递归计算子目录
				subPath := dirPath + "/" + obj.GetName()
				subSize, subFiles, err := c.calculateDirSize(ctx, subPath)
				if err != nil {
					log.Warnf("Failed to calculate size for subdirectory %s: %v", subPath, err)
					continue // 跳过错误的子目录，继续计算其他目录
				}
				totalSize += subSize
				totalFiles += subFiles
			} else {
				// 文件
				totalSize += obj.GetSize()
				totalFiles++
			}
			processedCount++
		}

		// 减少延时，只在处理大量文件时才休息
		if i+batchSize < len(objs) && len(objs) > 1000 {
			time.Sleep(5 * time.Millisecond) // 减少延时从10ms到5ms
		}
	}

	log.Debugf("Completed directory %s: %d bytes, %d files", dirPath, totalSize, totalFiles)
	return totalSize, totalFiles, nil
}

// UpdateIncremental 增量更新容量
func (c *Calculator) UpdateIncremental(ctx context.Context, basePath string, deltaBytes int64, deltaFiles int64) error {
	log.Debugf("Incremental capacity update for path %s: %+d bytes, %+d files", 
		basePath, deltaBytes, deltaFiles)
	
	// 获取当前容量记录
	capacity, err := c.persister.Get(basePath)
	if err != nil {
		log.Warnf("Failed to get existing capacity record for incremental update, will create new: %v", err)
		// 如果没有现有记录，创建新记录
		capacity = &model.BasePathCapacity{
			BasePath:          basePath,
			UsedBytes:         0,
			TotalFiles:        0,
			LastCalculatedAt:  time.Now(),
			Version:           1,
			CalculationStatus: "completed",
		}
	}
	
	// 更新容量
	capacity.UsedBytes += deltaBytes
	capacity.TotalFiles += deltaFiles
	capacity.LastCalculatedAt = time.Now()
	capacity.Version++
	
	// 确保不会出现负数
	if capacity.UsedBytes < 0 {
		log.Warnf("Capacity became negative for path %s, resetting to 0", basePath)
		capacity.UsedBytes = 0
	}
	if capacity.TotalFiles < 0 {
		log.Warnf("File count became negative for path %s, resetting to 0", basePath)
		capacity.TotalFiles = 0
	}
	
	// 保存到数据库
	if err := c.persister.Save(capacity); err != nil {
		return fmt.Errorf("failed to save incremental update: %w", err)
	}
	
	// 更新缓存
	c.cache.SetToL1(basePath, capacity)
	c.cache.SetToL2(basePath, capacity)
	
	return nil
}

// BatchCalculate 批量计算多个路径的容量
func (c *Calculator) BatchCalculate(ctx context.Context, basePaths []string) map[string]*model.BasePathCapacity {
	results := make(map[string]*model.BasePathCapacity)
	resultChan := make(chan struct {
		path     string
		capacity *model.BasePathCapacity
		err      error
	}, len(basePaths))
	
	// 并发计算
	for _, path := range basePaths {
		go func(p string) {
			capacity, err := c.Calculate(ctx, p)
			resultChan <- struct {
				path     string
				capacity *model.BasePathCapacity
				err      error
			}{p, capacity, err}
		}(path)
	}
	
	// 收集结果
	for i := 0; i < len(basePaths); i++ {
		result := <-resultChan
		if result.err != nil {
			log.Errorf("Failed to calculate capacity for path %s: %v", result.path, result.err)
		} else {
			results[result.path] = result.capacity
		}
	}
	
	return results
}

// GetStats 获取计算器统计信息
func (c *Calculator) GetStats() *CalculatorStats {
	return &CalculatorStats{
		TotalCalculations:    atomic.LoadInt64(&c.stats.TotalCalculations),
		SuccessCalculations:  atomic.LoadInt64(&c.stats.SuccessCalculations),
		FailedCalculations:   atomic.LoadInt64(&c.stats.FailedCalculations),
		TotalCalculationTime: atomic.LoadInt64(&c.stats.TotalCalculationTime),
		ActiveCalculations:   atomic.LoadInt64(&c.stats.ActiveCalculations),
	}
}

package capacity

import (
	"fmt"
	"sync/atomic"
	"time"

	"github.com/alist-org/alist/v3/internal/db"
	"github.com/alist-org/alist/v3/internal/model"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// Persister 数据库持久化管理器
type Persister struct {
	stats *PersisterStats
}

// PersisterStats 持久化统计信息
type PersisterStats struct {
	TotalReads   int64 // 总读取次数
	TotalWrites  int64 // 总写入次数
	SuccessReads int64 // 成功读取次数
	SuccessWrites int64 // 成功写入次数
	FailedReads  int64 // 失败读取次数
	FailedWrites int64 // 失败写入次数
}

// NewPersister 创建新的持久化管理器
func NewPersister() *Persister {
	return &Persister{
		stats: &PersisterStats{},
	}
}

// Get 获取基础路径容量信息
func (p *Persister) Get(basePath string) (*model.BasePathCapacity, error) {
	atomic.AddInt64(&p.stats.TotalReads, 1)
	
	var capacity model.BasePathCapacity
	err := db.GetDb().Where("base_path = ?", basePath).First(&capacity).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 记录不存在，这是正常情况
			atomic.AddInt64(&p.stats.SuccessReads, 1)
			return nil, nil
		}
		atomic.AddInt64(&p.stats.FailedReads, 1)
		return nil, errors.Wrapf(err, "failed to get capacity for base path: %s", basePath)
	}
	
	atomic.AddInt64(&p.stats.SuccessReads, 1)
	return &capacity, nil
}

// Save 保存基础路径容量信息
func (p *Persister) Save(capacity *model.BasePathCapacity) error {
	atomic.AddInt64(&p.stats.TotalWrites, 1)
	
	if capacity == nil {
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return fmt.Errorf("capacity is nil")
	}
	
	// 设置时间戳
	now := time.Now()
	if capacity.CreatedAt.IsZero() {
		capacity.CreatedAt = now
	}
	capacity.UpdatedAt = now
	
	// 使用事务确保数据一致性
	err := db.GetDb().Transaction(func(tx *gorm.DB) error {
		// 检查是否已存在记录
		var existing model.BasePathCapacity
		err := tx.Where("base_path = ?", capacity.BasePath).First(&existing).Error
		
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.Wrapf(err, "failed to check existing capacity record")
		}
		
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新记录
			if err := tx.Create(capacity).Error; err != nil {
				return errors.Wrapf(err, "failed to create capacity record")
			}
			log.Debugf("Created new capacity record for path: %s", capacity.BasePath)
		} else {
			// 更新现有记录，使用乐观锁
			result := tx.Model(&existing).
				Where("version = ?", existing.Version).
				Updates(map[string]interface{}{
					"used_bytes":         capacity.UsedBytes,
					"total_files":        capacity.TotalFiles,
					"last_calculated_at": capacity.LastCalculatedAt,
					"version":            existing.Version + 1,
					"calculation_status": capacity.CalculationStatus,
					"error_message":      capacity.ErrorMessage,
					"updated_at":         now,
				})
			
			if result.Error != nil {
				return errors.Wrapf(result.Error, "failed to update capacity record")
			}
			
			if result.RowsAffected == 0 {
				return fmt.Errorf("optimistic lock failed: capacity record was modified by another process")
			}
			
			// 更新传入对象的版本号
			capacity.Version = existing.Version + 1
			capacity.ID = existing.ID
			capacity.CreatedAt = existing.CreatedAt
			
			log.Debugf("Updated capacity record for path: %s (version: %d)", capacity.BasePath, capacity.Version)
		}
		
		return nil
	})
	
	if err != nil {
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return err
	}
	
	atomic.AddInt64(&p.stats.SuccessWrites, 1)
	return nil
}

// BatchSave 批量保存容量信息
func (p *Persister) BatchSave(capacities []*model.BasePathCapacity) error {
	if len(capacities) == 0 {
		return nil
	}
	
	atomic.AddInt64(&p.stats.TotalWrites, int64(len(capacities)))
	
	err := db.GetDb().Transaction(func(tx *gorm.DB) error {
		for _, capacity := range capacities {
			if capacity == nil {
				continue
			}
			
			// 设置时间戳
			now := time.Now()
			if capacity.CreatedAt.IsZero() {
				capacity.CreatedAt = now
			}
			capacity.UpdatedAt = now
			
			// 检查是否已存在记录
			var existing model.BasePathCapacity
			err := tx.Where("base_path = ?", capacity.BasePath).First(&existing).Error
			
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Errorf("Failed to check existing capacity record for %s: %v", capacity.BasePath, err)
				continue
			}
			
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 创建新记录
				if err := tx.Create(capacity).Error; err != nil {
					log.Errorf("Failed to create capacity record for %s: %v", capacity.BasePath, err)
					continue
				}
			} else {
				// 更新现有记录
				result := tx.Model(&existing).
					Where("version = ?", existing.Version).
					Updates(map[string]interface{}{
						"used_bytes":         capacity.UsedBytes,
						"total_files":        capacity.TotalFiles,
						"last_calculated_at": capacity.LastCalculatedAt,
						"version":            existing.Version + 1,
						"calculation_status": capacity.CalculationStatus,
						"error_message":      capacity.ErrorMessage,
						"updated_at":         now,
					})
				
				if result.Error != nil {
					log.Errorf("Failed to update capacity record for %s: %v", capacity.BasePath, result.Error)
					continue
				}
				
				if result.RowsAffected == 0 {
					log.Warnf("Optimistic lock failed for capacity record: %s", capacity.BasePath)
					continue
				}
				
				// 更新传入对象的版本号
				capacity.Version = existing.Version + 1
				capacity.ID = existing.ID
				capacity.CreatedAt = existing.CreatedAt
			}
		}
		
		return nil
	})
	
	if err != nil {
		atomic.AddInt64(&p.stats.FailedWrites, int64(len(capacities)))
		return errors.Wrapf(err, "failed to batch save capacity records")
	}
	
	atomic.AddInt64(&p.stats.SuccessWrites, int64(len(capacities)))
	return nil
}

// Delete 删除基础路径容量信息
func (p *Persister) Delete(basePath string) error {
	atomic.AddInt64(&p.stats.TotalWrites, 1)
	
	result := db.GetDb().Where("base_path = ?", basePath).Delete(&model.BasePathCapacity{})
	if result.Error != nil {
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return errors.Wrapf(result.Error, "failed to delete capacity record for base path: %s", basePath)
	}
	
	atomic.AddInt64(&p.stats.SuccessWrites, 1)
	log.Debugf("Deleted capacity record for path: %s (affected rows: %d)", basePath, result.RowsAffected)
	return nil
}

// DeleteByBasePaths 批量删除指定基础路径的容量记录（用于用户删除时）
func (p *Persister) DeleteByBasePaths(basePaths []string) error {
	if len(basePaths) == 0 {
		return nil
	}

	atomic.AddInt64(&p.stats.TotalWrites, int64(len(basePaths)))

	result := db.GetDb().Where("base_path IN ?", basePaths).Delete(&model.BasePathCapacity{})
	if result.Error != nil {
		atomic.AddInt64(&p.stats.FailedWrites, int64(len(basePaths)))
		return errors.Wrapf(result.Error, "failed to delete capacity records for base paths: %v", basePaths)
	}

	atomic.AddInt64(&p.stats.SuccessWrites, int64(len(basePaths)))
	log.Infof("Deleted capacity records for %d base paths (affected rows: %d)", len(basePaths), result.RowsAffected)
	return nil
}

// DeleteByUserID 删除指定用户的所有容量记录（用于用户删除时）
func (p *Persister) DeleteByUserID(userID uint) error {
	atomic.AddInt64(&p.stats.TotalWrites, 1)

	// 首先获取用户的所有基础路径
	var basePaths []string
	err := db.GetDb().Model(&model.UserBasePath{}).
		Where("user_id = ?", userID).
		Pluck("path", &basePaths).Error

	if err != nil {
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return errors.Wrapf(err, "failed to get base paths for user %d", userID)
	}

	if len(basePaths) == 0 {
		log.Debugf("No base paths found for user %d", userID)
		return nil
	}

	// 删除相关的容量记录
	result := db.GetDb().Where("base_path IN ?", basePaths).Delete(&model.BasePathCapacity{})
	if result.Error != nil {
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return errors.Wrapf(result.Error, "failed to delete capacity records for user %d", userID)
	}

	atomic.AddInt64(&p.stats.SuccessWrites, 1)
	log.Infof("Deleted capacity records for user %d: %d records deleted", userID, result.RowsAffected)
	return nil
}

// List 列出所有基础路径容量信息
func (p *Persister) List(limit, offset int) ([]*model.BasePathCapacity, error) {
	atomic.AddInt64(&p.stats.TotalReads, 1)
	
	var capacities []*model.BasePathCapacity
	query := db.GetDb().Order("last_calculated_at DESC")
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}
	
	err := query.Find(&capacities).Error
	if err != nil {
		atomic.AddInt64(&p.stats.FailedReads, 1)
		return nil, errors.Wrapf(err, "failed to list capacity records")
	}
	
	atomic.AddInt64(&p.stats.SuccessReads, 1)
	return capacities, nil
}

// GetExpired 获取过期的容量记录
func (p *Persister) GetExpired(expiredBefore time.Time) ([]*model.BasePathCapacity, error) {
	atomic.AddInt64(&p.stats.TotalReads, 1)
	
	var capacities []*model.BasePathCapacity
	err := db.GetDb().Where("last_calculated_at < ?", expiredBefore).Find(&capacities).Error
	
	if err != nil {
		atomic.AddInt64(&p.stats.FailedReads, 1)
		return nil, errors.Wrapf(err, "failed to get expired capacity records")
	}
	
	atomic.AddInt64(&p.stats.SuccessReads, 1)
	return capacities, nil
}

// GetByStatus 根据计算状态获取容量记录
func (p *Persister) GetByStatus(status string) ([]*model.BasePathCapacity, error) {
	atomic.AddInt64(&p.stats.TotalReads, 1)
	
	var capacities []*model.BasePathCapacity
	err := db.GetDb().Where("calculation_status = ?", status).Find(&capacities).Error
	
	if err != nil {
		atomic.AddInt64(&p.stats.FailedReads, 1)
		return nil, errors.Wrapf(err, "failed to get capacity records by status: %s", status)
	}
	
	atomic.AddInt64(&p.stats.SuccessReads, 1)
	return capacities, nil
}

// Cleanup 清理旧的容量记录 (仅供手动调用，不自动执行)
func (p *Persister) Cleanup(olderThan time.Time) (int64, error) {
	atomic.AddInt64(&p.stats.TotalWrites, 1)

	// 添加安全检查：只清理非常旧的记录（超过1年）
	oneYearAgo := time.Now().Add(-365 * 24 * time.Hour)
	if olderThan.After(oneYearAgo) {
		log.Warnf("Cleanup rejected: olderThan (%v) is too recent, minimum age is 1 year", olderThan)
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return 0, fmt.Errorf("cleanup rejected: minimum age for cleanup is 1 year")
	}

	log.Warnf("Manual cleanup requested for records older than: %v", olderThan)

	result := db.GetDb().Where("last_calculated_at < ? AND calculation_status != ?", olderThan, "error").
		Delete(&model.BasePathCapacity{})

	if result.Error != nil {
		atomic.AddInt64(&p.stats.FailedWrites, 1)
		return 0, errors.Wrapf(result.Error, "failed to cleanup old capacity records")
	}

	atomic.AddInt64(&p.stats.SuccessWrites, 1)
	log.Warnf("Manual cleanup completed: deleted %d old capacity records", result.RowsAffected)
	return result.RowsAffected, nil
}

// GetStats 获取持久化统计信息
func (p *Persister) GetStats() *PersisterStats {
	return &PersisterStats{
		TotalReads:    atomic.LoadInt64(&p.stats.TotalReads),
		TotalWrites:   atomic.LoadInt64(&p.stats.TotalWrites),
		SuccessReads:  atomic.LoadInt64(&p.stats.SuccessReads),
		SuccessWrites: atomic.LoadInt64(&p.stats.SuccessWrites),
		FailedReads:   atomic.LoadInt64(&p.stats.FailedReads),
		FailedWrites:  atomic.LoadInt64(&p.stats.FailedWrites),
	}
}

package capacity

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/alist-org/alist/v3/internal/model"
	log "github.com/sirupsen/logrus"
)

// Scheduler 定时任务调度器
type Scheduler struct {
	config     *Config
	calculator *Calculator
	persister  *Persister
	
	// 定时器
	updateTicker  *time.Ticker
	cleanupTicker *time.Ticker
	
	// 控制
	stopChan chan struct{}
	wg       sync.WaitGroup
	
	// 状态
	mu      sync.RWMutex
	running bool
	
	// 统计信息
	stats *SchedulerStats
}

// SchedulerStats 调度器统计信息
type SchedulerStats struct {
	TotalUpdateTasks   int64 // 总更新任务数
	SuccessUpdateTasks int64 // 成功更新任务数
	FailedUpdateTasks  int64 // 失败更新任务数
	TotalCleanupTasks  int64 // 总清理任务数
	SuccessCleanupTasks int64 // 成功清理任务数
	FailedCleanupTasks int64 // 失败清理任务数
	LastUpdateTime     int64 // 最后更新时间
	LastCleanupTime    int64 // 最后清理时间
}

// NewScheduler 创建新的调度器
func NewScheduler(config *Config, calculator *Calculator, persister *Persister) *Scheduler {
	return &Scheduler{
		config:     config,
		calculator: calculator,
		persister:  persister,
		stopChan:   make(chan struct{}),
		stats:      &SchedulerStats{},
	}
}

// Start 启动调度器
func (s *Scheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.running {
		return fmt.Errorf("scheduler is already running")
	}
	
	log.Info("Starting capacity scheduler...")
	
	// 启动定期更新任务
	s.updateTicker = time.NewTicker(s.config.UpdateInterval)
	s.wg.Add(1)
	go s.updateLoop()

	// 禁用自动清理任务 - 容量数据需要永久保留
	// s.cleanupTicker = time.NewTicker(s.config.CleanupInterval)
	// s.wg.Add(1)
	// go s.cleanupLoop()

	s.running = true
	log.Infof("Capacity scheduler started with update interval: %v (auto cleanup disabled)",
		s.config.UpdateInterval)
	
	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.running {
		return nil
	}
	
	log.Info("Stopping capacity scheduler...")
	
	// 停止定时器
	if s.updateTicker != nil {
		s.updateTicker.Stop()
	}
	// 清理定时器已禁用
	// if s.cleanupTicker != nil {
	//	s.cleanupTicker.Stop()
	// }
	
	// 发送停止信号
	close(s.stopChan)
	
	// 等待所有goroutine结束
	s.wg.Wait()
	
	s.running = false
	log.Info("Capacity scheduler stopped")
	
	return nil
}

// updateLoop 定期更新循环
func (s *Scheduler) updateLoop() {
	defer s.wg.Done()
	
	for {
		select {
		case <-s.updateTicker.C:
			s.performUpdate()
		case <-s.stopChan:
			return
		}
	}
}

// cleanupLoop 定期清理循环 (已禁用)
// func (s *Scheduler) cleanupLoop() {
//	defer s.wg.Done()
//
//	for {
//		select {
//		case <-s.cleanupTicker.C:
//			s.performCleanup()
//		case <-s.stopChan:
//			return
//		}
//	}
// }

// performUpdate 执行定期更新
func (s *Scheduler) performUpdate() {
	atomic.AddInt64(&s.stats.TotalUpdateTasks, 1)
	atomic.StoreInt64(&s.stats.LastUpdateTime, time.Now().Unix())
	
	log.Info("Starting scheduled capacity update...")
	
	ctx, cancel := context.WithTimeout(context.Background(), s.config.UpdateInterval/2)
	defer cancel()
	
	// 获取需要更新的容量记录
	expiredBefore := time.Now().Add(-s.config.UpdateInterval)
	expiredRecords, err := s.persister.GetExpired(expiredBefore)
	if err != nil {
		log.Errorf("Failed to get expired capacity records: %v", err)
		atomic.AddInt64(&s.stats.FailedUpdateTasks, 1)
		return
	}
	
	if len(expiredRecords) == 0 {
		log.Debug("No expired capacity records found")
		atomic.AddInt64(&s.stats.SuccessUpdateTasks, 1)
		return
	}
	
	log.Infof("Found %d expired capacity records to update", len(expiredRecords))
	
	// 批量更新
	successCount := 0
	failureCount := 0
	
	// 分批处理，避免一次性处理太多
	batchSize := s.config.BatchSize
	for i := 0; i < len(expiredRecords); i += batchSize {
		end := i + batchSize
		if end > len(expiredRecords) {
			end = len(expiredRecords)
		}
		
		batch := expiredRecords[i:end]
		s.updateBatch(ctx, batch, &successCount, &failureCount)
		
		// 检查是否需要停止
		select {
		case <-s.stopChan:
			log.Info("Update task interrupted by stop signal")
			return
		default:
		}
	}
	
	if failureCount == 0 {
		atomic.AddInt64(&s.stats.SuccessUpdateTasks, 1)
	} else {
		atomic.AddInt64(&s.stats.FailedUpdateTasks, 1)
	}
	
	log.Infof("Scheduled capacity update completed: %d success, %d failed", successCount, failureCount)
}

// updateBatch 批量更新容量记录
func (s *Scheduler) updateBatch(ctx context.Context, records []*model.BasePathCapacity, successCount, failureCount *int) {
	// 提取基础路径
	basePaths := make([]string, len(records))
	for i, record := range records {
		basePaths[i] = record.BasePath
	}
	
	// 批量计算
	results := s.calculator.BatchCalculate(ctx, basePaths)
	
	// 统计结果
	for _, record := range records {
		if _, ok := results[record.BasePath]; ok {
			*successCount++
		} else {
			*failureCount++
		}
	}
}

// performCleanup 执行定期清理 (已禁用自动调用，仅供手动触发)
func (s *Scheduler) performCleanup() {
	atomic.AddInt64(&s.stats.TotalCleanupTasks, 1)
	atomic.StoreInt64(&s.stats.LastCleanupTime, time.Now().Unix())

	log.Info("Starting manual cleanup...")

	// 注意：此方法现在仅用于手动清理，不会自动执行
	// 清理超过30天的旧记录
	olderThan := time.Now().Add(-30 * 24 * time.Hour)
	deletedCount, err := s.persister.Cleanup(olderThan)
	if err != nil {
		log.Errorf("Failed to cleanup old capacity records: %v", err)
		atomic.AddInt64(&s.stats.FailedCleanupTasks, 1)
		return
	}

	atomic.AddInt64(&s.stats.SuccessCleanupTasks, 1)
	log.Infof("Manual cleanup completed: deleted %d old records", deletedCount)
}

// TriggerUpdate 手动触发更新
func (s *Scheduler) TriggerUpdate(ctx context.Context, basePaths []string) error {
	if !s.running {
		return fmt.Errorf("scheduler is not running")
	}
	
	log.Infof("Manual update triggered for %d paths", len(basePaths))
	
	// 批量计算
	results := s.calculator.BatchCalculate(ctx, basePaths)
	
	successCount := len(results)
	failureCount := len(basePaths) - successCount
	
	log.Infof("Manual update completed: %d success, %d failed", successCount, failureCount)
	
	if failureCount > 0 {
		return fmt.Errorf("manual update partially failed: %d out of %d paths failed", failureCount, len(basePaths))
	}
	
	return nil
}

// TriggerCleanup 手动触发清理
func (s *Scheduler) TriggerCleanup(olderThan time.Time) (int64, error) {
	if !s.running {
		return 0, fmt.Errorf("scheduler is not running")
	}
	
	log.Info("Manual cleanup triggered")
	
	deletedCount, err := s.persister.Cleanup(olderThan)
	if err != nil {
		return 0, fmt.Errorf("manual cleanup failed: %w", err)
	}
	
	log.Infof("Manual cleanup completed: deleted %d records", deletedCount)
	return deletedCount, nil
}

// GetNextUpdateTime 获取下次更新时间
func (s *Scheduler) GetNextUpdateTime() time.Time {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	if !s.running || s.updateTicker == nil {
		return time.Time{}
	}
	
	lastUpdate := time.Unix(atomic.LoadInt64(&s.stats.LastUpdateTime), 0)
	if lastUpdate.IsZero() {
		// 如果还没有执行过更新，返回启动时间加上更新间隔
		return time.Now().Add(s.config.UpdateInterval)
	}
	
	return lastUpdate.Add(s.config.UpdateInterval)
}

// GetNextCleanupTime 获取下次清理时间
func (s *Scheduler) GetNextCleanupTime() time.Time {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	if !s.running || s.cleanupTicker == nil {
		return time.Time{}
	}
	
	lastCleanup := time.Unix(atomic.LoadInt64(&s.stats.LastCleanupTime), 0)
	if lastCleanup.IsZero() {
		// 如果还没有执行过清理，返回启动时间加上清理间隔
		return time.Now().Add(s.config.CleanupInterval)
	}
	
	return lastCleanup.Add(s.config.CleanupInterval)
}

// GetStats 获取调度器统计信息
func (s *Scheduler) GetStats() *SchedulerStats {
	return &SchedulerStats{
		TotalUpdateTasks:    atomic.LoadInt64(&s.stats.TotalUpdateTasks),
		SuccessUpdateTasks:  atomic.LoadInt64(&s.stats.SuccessUpdateTasks),
		FailedUpdateTasks:   atomic.LoadInt64(&s.stats.FailedUpdateTasks),
		TotalCleanupTasks:   atomic.LoadInt64(&s.stats.TotalCleanupTasks),
		SuccessCleanupTasks: atomic.LoadInt64(&s.stats.SuccessCleanupTasks),
		FailedCleanupTasks:  atomic.LoadInt64(&s.stats.FailedCleanupTasks),
		LastUpdateTime:      atomic.LoadInt64(&s.stats.LastUpdateTime),
		LastCleanupTime:     atomic.LoadInt64(&s.stats.LastCleanupTime),
	}
}

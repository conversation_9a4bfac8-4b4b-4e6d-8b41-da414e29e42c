package cookie_manager

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/alist-org/alist/v3/internal/db"
	"github.com/alist-org/alist/v3/internal/model"
	log "github.com/sirupsen/logrus"
)

// BaiduCookieManager 百度网盘Cookie管理器
type BaiduCookieManager struct {
	mu              sync.RWMutex
	client          *http.Client
	refreshInterval time.Duration
	maxRetries      int
	ctx             context.Context
	cancel          context.CancelFunc
	isRunning       bool
}

// CookieStatus Cookie状态
type CookieStatus struct {
	IsValid      bool      `json:"is_valid"`
	LastChecked  time.Time `json:"last_checked"`
	LastRefresh  time.Time `json:"last_refresh"`
	ErrorMessage string    `json:"error_message,omitempty"`
	StorageID    uint      `json:"storage_id"`
}

// NewBaiduCookieManager 创建新的百度网盘Cookie管理器
func NewBaiduCookieManager() *BaiduCookieManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &BaiduCookieManager{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        10,
				IdleConnTimeout:     30 * time.Second,
				DisableCompression:  true,
				DisableKeepAlives:   false,
			},
		},
		refreshInterval: 2 * time.Hour, // 每2小时检查一次
		maxRetries:      3,
		ctx:             ctx,
		cancel:          cancel,
		isRunning:       false,
	}
}

// Start 启动Cookie管理器
func (bcm *BaiduCookieManager) Start() {
	bcm.mu.Lock()
	defer bcm.mu.Unlock()
	
	if bcm.isRunning {
		log.Info("百度网盘Cookie管理器已在运行")
		return
	}
	
	bcm.isRunning = true
	log.Info("启动百度网盘Cookie自动刷新管理器")
	
	// 立即执行一次检查
	go bcm.checkAndRefreshCookies()
	
	// 启动定时任务
	go bcm.startScheduler()
}

// Stop 停止Cookie管理器
func (bcm *BaiduCookieManager) Stop() {
	bcm.mu.Lock()
	defer bcm.mu.Unlock()
	
	if !bcm.isRunning {
		return
	}
	
	bcm.cancel()
	bcm.isRunning = false
	log.Info("百度网盘Cookie管理器已停止")
}

// startScheduler 启动定时调度器
func (bcm *BaiduCookieManager) startScheduler() {
	ticker := time.NewTicker(bcm.refreshInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-bcm.ctx.Done():
			log.Info("Cookie管理器调度器已停止")
			return
		case <-ticker.C:
			bcm.checkAndRefreshCookies()
		}
	}
}

// checkAndRefreshCookies 检查并刷新所有百度网盘存储的Cookie
func (bcm *BaiduCookieManager) checkAndRefreshCookies() {
	log.Info("开始检查百度网盘Cookie状态...")
	
	// 获取所有百度网盘存储
	storages, err := bcm.getBaiduNetdiskStorages()
	if err != nil {
		log.Errorf("获取百度网盘存储失败: %v", err)
		return
	}
	
	if len(storages) == 0 {
		log.Info("未找到百度网盘存储配置")
		return
	}
	
	for _, storage := range storages {
		bcm.processStorage(storage)
	}
	
	log.Info("百度网盘Cookie检查完成")
}

// processStorage 处理单个存储的Cookie
func (bcm *BaiduCookieManager) processStorage(storage *model.Storage) {
	log.Infof("检查存储 %s (ID: %d) 的Cookie状态", storage.MountPath, storage.ID)
	
	// 解析存储配置
	var addition map[string]interface{}
	if err := json.Unmarshal([]byte(storage.Addition), &addition); err != nil {
		log.Errorf("解析存储 %s 配置失败: %v", storage.MountPath, err)
		return
	}
	
	// 获取当前Cookie
	currentCookie, ok := addition["cookie"].(string)
	if !ok || currentCookie == "" {
		log.Warnf("存储 %s 未配置Cookie", storage.MountPath)
		return
	}
	
	// 检查Cookie有效性
	isValid, err := bcm.validateCookie(currentCookie)
	if err != nil {
		log.Errorf("验证存储 %s Cookie失败: %v", storage.MountPath, err)
		return
	}
	
	if isValid {
		log.Infof("存储 %s 的Cookie有效", storage.MountPath)
		return
	}
	
	log.Warnf("存储 %s 的Cookie已失效，尝试刷新...", storage.MountPath)
	
	// 尝试刷新Cookie
	newCookie, err := bcm.refreshCookie(currentCookie)
	if err != nil {
		log.Errorf("刷新存储 %s Cookie失败: %v", storage.MountPath, err)
		return
	}
	
	// 更新存储配置
	if err := bcm.updateStorageCookie(storage, newCookie); err != nil {
		log.Errorf("更新存储 %s Cookie失败: %v", storage.MountPath, err)
		return
	}
	
	log.Infof("成功刷新存储 %s 的Cookie", storage.MountPath)
}

// getBaiduNetdiskStorages 获取所有百度网盘存储
func (bcm *BaiduCookieManager) getBaiduNetdiskStorages() ([]*model.Storage, error) {
	storages, err := db.GetEnabledStorages()
	if err != nil {
		return nil, err
	}
	
	var baiduStorages []*model.Storage
	for _, storage := range storages {
		if storage.Driver == "BaiduNetdisk" {
			baiduStorages = append(baiduStorages, &storage)
		}
	}
	
	return baiduStorages, nil
}

// validateCookie 验证Cookie是否有效
func (bcm *BaiduCookieManager) validateCookie(cookie string) (bool, error) {
	// 基本格式检查
	if !strings.Contains(cookie, "BAIDUID") || !strings.Contains(cookie, "BDUSS") {
		return false, fmt.Errorf("Cookie格式不正确，缺少必要字段")
	}
	
	// 发送API请求验证
	req, err := http.NewRequest("GET", "https://pan.baidu.com/api/quota", nil)
	if err != nil {
		return false, fmt.Errorf("创建验证请求失败: %v", err)
	}
	
	req.Header.Set("Cookie", cookie)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36")
	req.Header.Set("Referer", "https://pan.baidu.com")
	
	resp, err := bcm.client.Do(req)
	if err != nil {
		return false, fmt.Errorf("验证请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 检查响应状态
	if resp.StatusCode == 401 || resp.StatusCode == 403 {
		return false, nil
	}
	
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("验证请求返回异常状态码: %d", resp.StatusCode)
	}
	
	// 读取响应内容进行进一步验证
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, fmt.Errorf("读取验证响应失败: %v", err)
	}
	
	// 检查响应内容是否包含错误信息
	bodyStr := string(body)
	if strings.Contains(bodyStr, "\"errno\":-6") || strings.Contains(bodyStr, "\"errno\":-4") {
		return false, nil
	}
	
	return true, nil
}

// refreshCookie 刷新Cookie
func (bcm *BaiduCookieManager) refreshCookie(oldCookie string) (string, error) {
	// 提取BDUSS
	bduss := bcm.extractBDUSS(oldCookie)
	if bduss == "" {
		return "", fmt.Errorf("无法从Cookie中提取BDUSS")
	}
	
	// 使用BDUSS获取新的Cookie
	newCookie, err := bcm.getCookieFromBDUSS(bduss)
	if err != nil {
		return "", fmt.Errorf("使用BDUSS获取新Cookie失败: %v", err)
	}
	
	return newCookie, nil
}

// extractBDUSS 从Cookie中提取BDUSS
func (bcm *BaiduCookieManager) extractBDUSS(cookie string) string {
	re := regexp.MustCompile(`BDUSS=([^;]+)`)
	matches := re.FindStringSubmatch(cookie)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// getCookieFromBDUSS 使用BDUSS获取新的Cookie
func (bcm *BaiduCookieManager) getCookieFromBDUSS(bduss string) (string, error) {
	// 构建新的Cookie字符串
	// 这里实现Cookie刷新的核心逻辑
	// 通常需要访问百度的登录接口来获取新的session信息
	
	// 访问百度网盘主页获取新的session信息
	req, err := http.NewRequest("GET", "https://pan.baidu.com", nil)
	if err != nil {
		return "", fmt.Errorf("创建刷新请求失败: %v", err)
	}
	
	// 设置基础Cookie
	req.Header.Set("Cookie", fmt.Sprintf("BDUSS=%s", bduss))
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36")
	
	resp, err := bcm.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("刷新请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 从响应头中提取新的Cookie
	var cookieParts []string
	cookieParts = append(cookieParts, fmt.Sprintf("BDUSS=%s", bduss))
	
	// 提取Set-Cookie头中的有用信息
	for _, cookie := range resp.Cookies() {
		if cookie.Name == "BAIDUID" || cookie.Name == "BIDUPSID" || 
		   cookie.Name == "PSTM" || cookie.Name == "BDORZ" {
			cookieParts = append(cookieParts, fmt.Sprintf("%s=%s", cookie.Name, cookie.Value))
		}
	}
	
	newCookie := strings.Join(cookieParts, "; ")
	
	// 验证新Cookie是否有效
	isValid, err := bcm.validateCookie(newCookie)
	if err != nil {
		return "", fmt.Errorf("验证新Cookie失败: %v", err)
	}
	
	if !isValid {
		return "", fmt.Errorf("刷新后的Cookie仍然无效")
	}
	
	return newCookie, nil
}

// updateStorageCookie 更新存储的Cookie配置
func (bcm *BaiduCookieManager) updateStorageCookie(storage *model.Storage, newCookie string) error {
	// 解析当前配置
	var addition map[string]interface{}
	if err := json.Unmarshal([]byte(storage.Addition), &addition); err != nil {
		return fmt.Errorf("解析存储配置失败: %v", err)
	}
	
	// 更新Cookie
	addition["cookie"] = newCookie
	
	// 序列化配置
	newAddition, err := json.Marshal(addition)
	if err != nil {
		return fmt.Errorf("序列化存储配置失败: %v", err)
	}
	
	// 更新数据库
	storage.Addition = string(newAddition)
	if err := db.UpdateStorage(storage); err != nil {
		return fmt.Errorf("更新数据库失败: %v", err)
	}
	
	return nil
}

// GetCookieStatus 获取Cookie状态
func (bcm *BaiduCookieManager) GetCookieStatus() ([]CookieStatus, error) {
	storages, err := bcm.getBaiduNetdiskStorages()
	if err != nil {
		return nil, err
	}
	
	var statuses []CookieStatus
	for _, storage := range storages {
		var addition map[string]interface{}
		if err := json.Unmarshal([]byte(storage.Addition), &addition); err != nil {
			continue
		}
		
		cookie, ok := addition["cookie"].(string)
		if !ok || cookie == "" {
			continue
		}
		
		isValid, err := bcm.validateCookie(cookie)
		status := CookieStatus{
			IsValid:     isValid,
			LastChecked: time.Now(),
			StorageID:   storage.ID,
		}
		
		if err != nil {
			status.ErrorMessage = err.Error()
		}
		
		statuses = append(statuses, status)
	}
	
	return statuses, nil
}

// ForceRefresh 强制刷新指定存储的Cookie
func (bcm *BaiduCookieManager) ForceRefresh(storageID uint) error {
	storage, err := db.GetStorageById(storageID)
	if err != nil {
		return fmt.Errorf("获取存储失败: %v", err)
	}

	if storage.Driver != "BaiduNetdisk" {
		return fmt.Errorf("存储类型不是百度网盘")
	}

	bcm.processStorage(storage)
	return nil
}

// ValidateCookie 公开的Cookie验证方法
func (bcm *BaiduCookieManager) ValidateCookie(cookie string) (bool, error) {
	return bcm.validateCookie(cookie)
}

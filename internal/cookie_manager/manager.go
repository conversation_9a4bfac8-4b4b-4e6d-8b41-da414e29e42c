package cookie_manager

import (
	"sync"

	log "github.com/sirupsen/logrus"
)

// Manager Cookie管理器的全局管理器
type Manager struct {
	baiduManager *BaiduCookieManager
	mu           sync.RWMutex
	started      bool
}

var (
	globalManager *Manager
	once          sync.Once
)

// GetManager 获取全局Cookie管理器实例
func GetManager() *Manager {
	once.Do(func() {
		globalManager = &Manager{
			baiduManager: NewBaiduCookieManager(),
		}
	})
	return globalManager
}

// Start 启动所有Cookie管理器
func (m *Manager) Start() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.started {
		log.Info("Cookie管理器已启动")
		return
	}
	
	log.Info("启动Cookie管理器服务...")
	
	// 启动百度网盘Cookie管理器
	m.baiduManager.Start()
	
	m.started = true
	log.Info("Cookie管理器服务启动完成")
}

// Stop 停止所有Cookie管理器
func (m *Manager) Stop() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if !m.started {
		return
	}
	
	log.Info("停止Cookie管理器服务...")
	
	// 停止百度网盘Cookie管理器
	m.baiduManager.Stop()
	
	m.started = false
	log.Info("Cookie管理器服务已停止")
}

// GetBaiduManager 获取百度网盘Cookie管理器
func (m *Manager) GetBaiduManager() *BaiduCookieManager {
	return m.baiduManager
}

// IsStarted 检查管理器是否已启动
func (m *Manager) IsStarted() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.started
}

// 便捷函数

// StartCookieManager 启动Cookie管理器
func StartCookieManager() {
	GetManager().Start()
}

// StopCookieManager 停止Cookie管理器
func StopCookieManager() {
	GetManager().Stop()
}

// GetBaiduCookieManager 获取百度网盘Cookie管理器
func GetBaiduCookieManager() *BaiduCookieManager {
	return GetManager().GetBaiduManager()
}

// RefreshBaiduCookie 强制刷新百度网盘Cookie
func RefreshBaiduCookie(storageID uint) error {
	return GetManager().GetBaiduManager().ForceRefresh(storageID)
}

// GetBaiduCookieStatus 获取百度网盘Cookie状态
func GetBaiduCookieStatus() ([]CookieStatus, error) {
	return GetManager().GetBaiduManager().GetCookieStatus()
}

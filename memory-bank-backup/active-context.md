# AList 项目活跃上下文

## 当前状态

**项目状态**：生产就绪，持续开发中
**最后更新**：2024年12月
**当前版本**：dev (开发版本)
**部署状态**：本地开发环境运行中

## 正在进行的任务

### 1. 项目熟悉和文档整理
- ✅ 完成项目结构分析
- ✅ 完成核心功能模块梳理
- ✅ 完成技术架构文档
- ✅ 完成memory-bank初始化

### 2. 开发环境状态
- **后端服务**：Go 1.23.4
- **前端构建**：SolidJS + Vite
- **数据库**：SQLite (data/data.db)
- **端口**：5244 (HTTP)
- **管理员**：admin (密码首次启动生成)

### 3. 最新功能状态
- ✅ **分享转存功能**：百度网盘批量转存完全实现
- ✅ **容量管理系统**：实时监控和WebSocket推送
- ✅ **多基础路径用户系统**：用户隔离和权限控制
- ✅ **Cookie自动刷新**：百度网盘Cookie管理



## 当前工作重点

### 架构师模式激活
- 🎯 **当前模式**：architect
- 🔍 **关注点**：系统架构分析和优化建议
- 📋 **任务**：项目全面熟悉和技术评估

### 技术债务识别
- 代码质量评估
- 性能瓶颈分析
- 安全漏洞检查
- 架构改进建议

### 功能完善计划
- 其他云盘转存支持
- 前端UI/UX优化
- API文档完善
- 测试覆盖率提升

## 关键文件和目录

### 核心配置文件
- `config.json` - 主配置文件
- `data.db` - SQLite数据库
- `go.mod` - Go模块依赖

### 重要代码目录
- `internal/` - 核心业务逻辑
- `server/handles/` - API处理器
- `drivers/` - 存储驱动
- `alist-web/src/` - 前端源码

### 文档目录
- `docs/` - 项目文档
- `README.md` - 项目说明
- `.amazonq/rules/` - 开发规则

## 开发环境配置

### 必需工具
- Go 1.23.4+
- Node.js (前端开发)
- Git
- Docker (可选)

### 环境变量
```bash
# 代理设置（网络问题时）
export http_proxy=socks5://127.0.0.1:10808
export https_proxy=socks5://127.0.0.1:10808

# MySQL配置（如需要）
MYSQL_ROOT_PASSWORD=Hykj**$$
SUDO_PASSWORD=8844
ADMIN_PASSWORD=8844
```

### 启动命令
```bash
# 后端开发
go run main.go server

# 前端开发
cd alist-web && pnpm dev

# 构建前端
cd alist-web && pnpm build
```

## 已知问题和解决方案

### 1. 前端文件缺失
- **问题**：`index.html not exist`
- **解决**：下载前端构建文件到 `public/dist/`

### 2. 网络连接问题
- **问题**：GitHub访问失败
- **解决**：使用代理或镜像源

### 3. 外部工具警告
- **现象**：qBittorrent/aria2 failed
- **说明**：正常现象，这些工具未安装

## 技术栈总结

### 后端核心
- **语言**：Go 1.23.4
- **框架**：Gin
- **数据库**：SQLite/MySQL/PostgreSQL
- **ORM**：GORM
- **认证**：JWT + 2FA + WebAuthn

### 前端核心
- **框架**：SolidJS
- **构建**：Vite
- **UI库**：Hope UI
- **状态管理**：SolidJS内置

### 基础设施
- **容器化**：Docker
- **反向代理**：Nginx/Caddy
- **监控**：内置日志系统
- **部署**：多种方式支持

## 下一步计划

### 即时任务
1. 完成项目架构分析
2. 识别优化机会
3. 提供改进建议
4. 更新技术文档

### 短期目标
1. 代码质量评估
2. 性能基准测试
3. 安全审计
4. 用户体验优化

### 长期规划
1. 架构重构建议
2. 新功能开发
3. 生态系统扩展
4. 社区贡献

## 联系信息

### 项目资源
- **官方文档**：https://alist.nn.ci/
- **源码仓库**：https://github.com/alist-org/alist
- **容器镜像**：xhofe/alist
- **演示站点**：https://al.nn.ci

### 社区支持
- GitHub Discussions
- Discord: https://discord.gg/F4ymsH4xv2
- Telegram: https://t.me/alist_chat

---

*上下文更新时间：2024年12月*
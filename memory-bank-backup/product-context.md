# AList 项目产品上下文

## 项目概述

**AList** 是一个功能强大的多存储文件列表程序，支持80+种云存储服务，使用Go语言开发后端，SolidJS开发前端。它提供了统一的Web界面来管理和访问各种云存储服务中的文件。

### 核心价值主张
- 🗂️ **统一文件管理**：一个界面管理多个云存储账号
- 🌐 **广泛兼容性**：支持80+种主流云存储服务
- 🚀 **开箱即用**：简单部署，无需复杂配置
- 🔐 **企业级安全**：多种认证方式，权限精细控制
- 📱 **全平台支持**：Web、移动端、桌面端完美适配

### 技术架构

#### 后端技术栈
- **语言**：Go 1.23.4+
- **Web框架**：Gin (高性能HTTP路由框架)
- **数据库**：SQLite/MySQL/PostgreSQL (GORM ORM)
- **认证**：JWT + WebAuthn + OAuth2 + 2FA
- **协议支持**：HTTP/HTTPS、WebDAV、FTP、SFTP、S3 API

#### 前端技术栈
- **框架**：SolidJS (高性能响应式框架)
- **构建工具**：Vite
- **UI组件**：Hope UI + 自定义组件库
- **国际化**：多语言支持

#### 部署架构
- **容器化**：Docker + Docker Compose
- **反向代理**：Nginx、Caddy、Apache
- **进程管理**：内置守护进程
- **负载均衡**：多实例部署支持

## 核心功能模块

### 1. 存储驱动系统
- **位置**：`drivers/` 目录
- **支持存储**：80+种云存储服务

### 2. 文件系统抽象层
- **位置**：`internal/fs/` 目录
- **功能**：统一文件操作接口

### 3. Web API 服务
- **位置**：`server/handles/` 目录
- **主要API**：认证、文件系统、管理功能

### 4. 用户认证系统
- **位置**：`internal/authn/` 目录
- **认证方式**：JWT、2FA、LDAP、SSO、WebAuthn

### 5. 搜索系统
- **位置**：`internal/search/` 目录
- **支持引擎**：Bleve、Meilisearch、数据库搜索

## 最新功能开发

### 1. 分享转存功能 ✅
- 支持百度网盘分享链接批量转存
- 自动识别链接类型
- 批量转存处理

### 2. 容量管理系统 ✅
- 实时容量监控和自动清理
- WebSocket实时推送
- 增量容量更新

### 3. 多基础路径用户系统 ✅
- 支持用户级别的路径隔离
- 用户专属存储空间
- 路径级权限控制

### 4. Cookie自动刷新机制 ✅
- 百度网盘Cookie自动刷新
- 定时检查有效性
- 自动刷新过期Cookie

---

*导出时间：2024年12月*
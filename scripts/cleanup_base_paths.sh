#!/bin/bash

# AList基础路径清理脚本
# 用于处理删除用户基础路径后的文件夹清理工作

QUEUE_FILE="data/cleanup_queue.jsonl"
API_BASE_URL="http://localhost:5244/api/admin"
LOG_FILE="data/cleanup.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查队列文件是否存在
if [ ! -f "$QUEUE_FILE" ]; then
    log "Queue file $QUEUE_FILE does not exist, nothing to clean up"
    exit 0
fi

log "Starting base path cleanup service..."

# 读取队列文件并处理每个清理请求
while IFS= read -r line; do
    if [ -z "$line" ]; then
        continue
    fi
    
    # 解析JSON获取base_path和status
    base_path=$(echo "$line" | jq -r '.base_path')
    status=$(echo "$line" | jq -r '.status')
    
    # 只处理待处理的请求
    if [ "$status" = "pending" ]; then
        log "Processing cleanup request for base path: $base_path"
        
        # 调用清理API
        response=$(curl -s -X POST "$API_BASE_URL/cleanup/base_path_folder?path=$base_path" \
                   -H "Content-Type: application/json")
        
        if [ $? -eq 0 ]; then
            log "Successfully requested cleanup for base path: $base_path"
            # 更新状态为completed
            updated_line=$(echo "$line" | jq '.status = "completed"')
        else
            log "Failed to cleanup base path: $base_path"
            # 更新状态为failed
            updated_line=$(echo "$line" | jq '.status = "failed"')
        fi
        
        # 这里可以将更新后的状态写回文件
        # 为了简化，我们暂时只记录日志
    fi
    
done < "$QUEUE_FILE"

log "Base path cleanup service completed"

# 可选：清理已完成的请求（保留最近7天的记录）
# find data -name "cleanup_queue.jsonl" -mtime +7 -exec rm {} \;

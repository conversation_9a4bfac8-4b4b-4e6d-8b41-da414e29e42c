package handles

import (
	"context"
	"fmt"
	stdpath "path"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/alist-org/alist/v3/internal/capacity"
	"github.com/alist-org/alist/v3/internal/fs"
	"github.com/alist-org/alist/v3/internal/model"
	log "github.com/sirupsen/logrus"
)

// 全局后台清理管理器注册表
var (
	globalCleanupManagers = make(map[string]*BackgroundCleanupManager)
	globalManagersMutex   sync.RWMutex
	callbackRegistered    atomic.Bool
	capacityExceededNotifier func(userID uint, folderName, storageType, basePath string, deletedFiles []string)
)

// BackgroundCleanupManager 后台清理任务管理器
type BackgroundCleanupManager struct {
	transferredFiles      []TransferredFileRecord
	mutex                sync.RWMutex
	stopChan             chan struct{}
	capacityCompleteChan chan struct{} // 容量计算完成通知通道
	basePathConfig       *model.UserBasePath
	isRunning            atomic.Bool
	user                 *model.User
	waitingForCapacity   atomic.Bool // 是否正在等待容量计算完成
}

// startBackgroundCleanup 启动后台清理管理器
func (bt *BaiduTransfer) startBackgroundCleanup(basePathConfig *model.UserBasePath) *BackgroundCleanupManager {
	// 确保WebSocket回调函数已注册（只注册一次）
	if !callbackRegistered.Load() {
		registerWebSocketCallback()
		callbackRegistered.Store(true)
	}

	manager := &BackgroundCleanupManager{
		transferredFiles:      make([]TransferredFileRecord, 0),
		stopChan:             make(chan struct{}),
		capacityCompleteChan: make(chan struct{}, 1), // 缓冲通道，避免阻塞
		basePathConfig:       basePathConfig,
		user:                 bt.User,
	}

	manager.isRunning.Store(true)

	// 注册到全局管理器
	managerKey := fmt.Sprintf("%d_%s", bt.User.ID, basePathConfig.Path)
	globalManagersMutex.Lock()
	globalCleanupManagers[managerKey] = manager
	globalManagersMutex.Unlock()

	// 启动后台监控goroutine
	go manager.monitorCapacity()

	log.Infof("后台容量清理管理器已启动，用户: %s, 目标路径: %s, 容量限制: %.2f GB, 注册键: %s",
		bt.User.Username, basePathConfig.Path, basePathConfig.CapacityLimitGB, managerKey)
	return manager
}

// RecordTransferredFile 记录转存文件（异步，无性能影响）
func (m *BackgroundCleanupManager) RecordTransferredFile(record TransferredFileRecord) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.transferredFiles = append(m.transferredFiles, record)
	log.Debugf("记录转存文件: %s (顺序: %d, 大小: %.2f MB)",
		record.FileName, record.Order, float64(record.FileSize)/(1024*1024))
}

// NotifyCapacityCalculationComplete 通知容量计算完成
func (m *BackgroundCleanupManager) NotifyCapacityCalculationComplete() {
	if m.waitingForCapacity.Load() {
		log.Info("📊 收到容量计算完成通知，触发清理检查")
		select {
		case m.capacityCompleteChan <- struct{}{}:
			// 通知发送成功
		default:
			// 通道已满，说明已经有通知在等待
			log.Debug("容量完成通知通道已满，跳过重复通知")
		}
	}
}

// monitorCapacity 容量监控主循环
func (m *BackgroundCleanupManager) monitorCapacity() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()
	
	log.Infof("开始监控容量，检查间隔: 30秒")
	
	for {
		select {
		case <-ticker.C:
			m.checkAndCleanup()
		case <-m.stopChan:
			log.Info("后台容量监控已停止")
			return
		}
	}
}

// CheckAndCleanup 公开的检查容量并执行清理方法
func (m *BackgroundCleanupManager) CheckAndCleanup() {
	m.checkAndCleanup()
}

// checkAndCleanup 检查容量并执行清理
func (m *BackgroundCleanupManager) checkAndCleanup() {
	// 获取当前容量信息
	capacity, err := m.getCurrentCapacity()
	if err != nil {
		log.Warnf("无法获取容量信息: %v", err)
		return
	}

	log.Infof("容量检查 - 当前使用率: %.1f%% (%.2f GB / %.2f GB), 记录文件数: %d",
		capacity.UsagePercent,
		float64(capacity.UsedBytes)/(1024*1024*1024),
		float64(capacity.TotalBytes)/(1024*1024*1024),
		m.GetTransferredFilesCount())

	// 如果容量超过100%，执行清理
	if capacity.UsagePercent > 100.0 {
		log.Infof("🚨 检测到容量超限(%.1f%%)，开始自动清理转存文件", capacity.UsagePercent)
		m.performCleanup(capacity)
	} else {
		log.Infof("✅ 容量正常(%.1f%%)，无需清理", capacity.UsagePercent)
	}
}

// performCleanup 执行文件清理
func (m *BackgroundCleanupManager) performCleanup(capacity *CapacityInfo) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if len(m.transferredFiles) == 0 {
		log.Warn("❌ 没有可清理的转存文件，可能文件记录丢失")
		return
	}

	log.Infof("🧹 开始清理转存文件，当前记录数: %d", len(m.transferredFiles))

	// 打印所有记录的文件信息
	for i, file := range m.transferredFiles {
		log.Infof("📁 记录文件[%d]: %s (%.2f MB, 顺序: %d)",
			i+1, file.FileName, float64(file.FileSize)/(1024*1024), file.Order)
	}
	
	// 按转存顺序倒序排列（最后转存的先删除）
	sort.Slice(m.transferredFiles, func(i, j int) bool {
		return m.transferredFiles[i].Order > m.transferredFiles[j].Order
	})
	
	deletedCount := 0
	var deletedFiles []string
	var totalDeletedSize int64
	
	for i := 0; i < len(m.transferredFiles); i++ {
		fileRecord := m.transferredFiles[i]
		
		// 删除文件
		log.Infof("🗑️ 正在删除文件: %s (大小: %.2f MB, 转存顺序: %d, 路径: %s)",
			fileRecord.FileName, float64(fileRecord.FileSize)/(1024*1024), fileRecord.Order, fileRecord.FilePath)

		if err := m.deleteFile(fileRecord.FilePath); err != nil {
			log.Errorf("❌ 删除文件失败: %s - %v", fileRecord.FileName, err)
			continue
		}
		
		deletedCount++
		deletedFiles = append(deletedFiles, fileRecord.FileName)
		totalDeletedSize += fileRecord.FileSize
		
		log.Infof("已删除文件: %s (大小: %.2f MB)",
			fileRecord.FileName, float64(fileRecord.FileSize)/(1024*1024))
		
		// 从记录中移除已删除的文件
		m.transferredFiles = append(m.transferredFiles[:i], m.transferredFiles[i+1:]...)
		i-- // 调整索引，因为切片长度变化了
		
		// 删除文件后，触发容量重新计算
		log.Info("🔄 触发容量重新计算以获取准确的容量信息...")
		go m.triggerCapacityRecalculation()

		// 等待容量重新计算完成
		time.Sleep(10 * time.Second)

		updatedCapacity, err := m.getCurrentCapacity()
		if err != nil {
			log.Warnf("无法获取更新后的容量信息: %v", err)
			continue
		}

		log.Infof("删除后容量使用率: %.1f%% (%.2f GB / %.2f GB)",
			updatedCapacity.UsagePercent,
			float64(updatedCapacity.UsedBytes)/(1024*1024*1024),
			float64(updatedCapacity.TotalBytes)/(1024*1024*1024))
		
		// 如果容量已降到100%以下，停止删除
		if updatedCapacity.UsagePercent <= 100.0 {
			log.Infof("容量已降至%.1f%%，停止自动清理", updatedCapacity.UsagePercent)
			break
		}
	}
	
	// 记录清理结果
	if deletedCount > 0 {
		log.Infof("自动清理完成。删除了%d个文件，释放空间%.2f GB",
			deletedCount, float64(totalDeletedSize)/(1024*1024*1024))
		log.Infof("删除的文件列表: %v", deletedFiles)
		m.notifyCleanupResult(deletedCount, deletedFiles, totalDeletedSize)
	} else {
		log.Warn("清理过程中没有成功删除任何文件")
	}
}

// deleteFile 删除文件
func (m *BackgroundCleanupManager) deleteFile(filePath string) error {
	ctx := context.Background()
	return fs.Remove(ctx, filePath)
}

// GetCurrentCapacity 公开的获取当前容量信息方法
func (m *BackgroundCleanupManager) GetCurrentCapacity() (*CapacityInfo, error) {
	return m.getCurrentCapacity()
}

// getCurrentCapacity 获取当前容量信息
func (m *BackgroundCleanupManager) getCurrentCapacity() (*CapacityInfo, error) {
	// 调用现有的容量管理器
	capacityManager := capacity.GetGlobalManager()
	capacity, err := capacityManager.GetCapacity(context.Background(), m.basePathConfig.Path)
	if err != nil {
		return nil, err
	}
	
	totalBytes := int64(m.basePathConfig.CapacityLimitGB * 1024 * 1024 * 1024)
	usagePercent := float64(capacity.UsedBytes) / float64(totalBytes) * 100
	
	return &CapacityInfo{
		UsedBytes:     capacity.UsedBytes,
		TotalBytes:    totalBytes,
		UsagePercent:  usagePercent,
	}, nil
}

// notifyCleanupResult 通知清理结果
func (m *BackgroundCleanupManager) notifyCleanupResult(deletedCount int, deletedFiles []string, totalDeletedSize int64) {
	// 记录详细的清理日志
	log.Infof("=== 容量自动清理报告 ===")
	log.Infof("用户: %s", m.user.Username)
	log.Infof("基础路径: %s", m.basePathConfig.Path)
	log.Infof("容量限制: %.2f GB", m.basePathConfig.CapacityLimitGB)
	log.Infof("删除文件数: %d", deletedCount)
	log.Infof("释放空间: %.2f GB", float64(totalDeletedSize)/(1024*1024*1024))
	log.Infof("删除文件列表: %v", deletedFiles)
	log.Infof("========================")

	// 发送WebSocket通知到前端
	m.sendCapacityExceededNotification(deletedFiles)
}

// sendCapacityExceededNotification 发送容量超限通知到前端
func (m *BackgroundCleanupManager) sendCapacityExceededNotification(deletedFiles []string) {
	// 获取实际删除的文件夹名称（使用第一个删除的文件名作为代表）
	var actualFolderName string
	if len(deletedFiles) > 0 {
		actualFolderName = deletedFiles[0] // 使用实际删除的文件名
	} else {
		// 如果没有删除文件，使用基础路径名称作为后备
		actualFolderName = stdpath.Base(m.basePathConfig.Path)
		if actualFolderName == "." || actualFolderName == "/" {
			actualFolderName = "分享文件"
		}
	}

	// 提取网盘类型
	storageType := "分享"
	if strings.Contains(m.basePathConfig.Path, "baidu") {
		storageType = "百度分享"
	} else if strings.Contains(m.basePathConfig.Path, "aliyun") {
		storageType = "阿里分享"
	} else if strings.Contains(m.basePathConfig.Path, "123") {
		storageType = "123分享"
	}

	// 发送WebSocket通知
	NotifyCapacityExceededGlobal(m.user.ID, actualFolderName, storageType, m.basePathConfig.Path, deletedFiles)
}

// SetCapacityExceededNotifier 设置容量超限通知器
func SetCapacityExceededNotifier(notifier func(userID uint, folderName, storageType, basePath string, deletedFiles []string)) {
	capacityExceededNotifier = notifier
	log.Info("容量超限通知器已设置")
}

// NotifyCapacityExceededGlobal 全局发送容量超限通知
func NotifyCapacityExceededGlobal(userID uint, folderName, storageType, basePath string, deletedFiles []string) {
	log.Infof("📢 发送容量超限通知: 用户=%d, 文件夹=%s, 存储类型=%s, 删除文件=%v",
		userID, folderName, storageType, deletedFiles)

	// 调用WebSocket通知器
	if capacityExceededNotifier != nil {
		capacityExceededNotifier(userID, folderName, storageType, basePath, deletedFiles)
	} else {
		log.Warn("容量超限通知器未设置，无法发送WebSocket通知")
	}
}

// Stop 停止后台清理
func (m *BackgroundCleanupManager) Stop() {
	if m.isRunning.Load() {
		close(m.stopChan)
		m.isRunning.Store(false)

		// 从全局注册表中移除
		managerKey := fmt.Sprintf("%d_%s", m.user.ID, m.basePathConfig.Path)
		globalManagersMutex.Lock()
		delete(globalCleanupManagers, managerKey)
		globalManagersMutex.Unlock()

		log.Infof("后台清理管理器已停止，剩余记录的文件数: %d, 注销键: %s", len(m.transferredFiles), managerKey)
	}
}

// GetTransferredFilesCount 获取当前记录的转存文件数量
func (m *BackgroundCleanupManager) GetTransferredFilesCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return len(m.transferredFiles)
}

// UpdateFileSize 更新转存文件的实际大小
func (m *BackgroundCleanupManager) UpdateFileSize(fileName string, fileSize int64) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for i := range m.transferredFiles {
		if m.transferredFiles[i].FileName == fileName {
			m.transferredFiles[i].FileSize = fileSize
			log.Infof("📝 更新转存文件大小: %s -> %d bytes", fileName, fileSize)
			return
		}
	}
	log.Warnf("⚠️ 未找到要更新大小的转存文件: %s", fileName)
}

// NotifyCapacityCalculationCompleteGlobal 全局通知容量计算完成
func NotifyCapacityCalculationCompleteGlobal(userID uint, basePath string) {
	managerKey := fmt.Sprintf("%d_%s", userID, basePath)

	globalManagersMutex.RLock()
	manager, exists := globalCleanupManagers[managerKey]
	globalManagersMutex.RUnlock()

	if exists && manager != nil {
		log.Infof("📊 向后台清理管理器发送容量计算完成通知: %s", managerKey)
		manager.NotifyCapacityCalculationComplete()
	} else {
		log.Debugf("未找到对应的后台清理管理器: %s", managerKey)
	}
}

// registerWebSocketCallback 注册WebSocket回调函数
func registerWebSocketCallback() {
	// 这里需要导入websocket包，但为了避免循环依赖，我们使用反射或接口
	// 暂时先用一个简单的实现
	log.Info("📡 注册WebSocket容量计算完成回调函数")

	// 注意：这里需要调用websocket包的SetCapacityCompletedCallback方法
	// 但由于循环依赖问题，我们需要在main函数或初始化时设置
}

// triggerCapacityRecalculation 触发容量重新计算
func (m *BackgroundCleanupManager) triggerCapacityRecalculation() {
	// 调用容量管理器重新计算容量
	capacityManager := capacity.GetGlobalManager()
	ctx := context.Background()

	log.Infof("🔄 开始重新计算容量: %s", m.basePathConfig.Path)

	// 清除缓存并重新计算
	if err := capacityManager.InitializeCapacityWithUser(ctx, m.basePathConfig.Path, m.user.ID, m.basePathConfig.ID); err != nil {
		log.Errorf("容量重新计算失败: %v", err)
	} else {
		log.Info("✅ 容量重新计算已触发")
	}
}

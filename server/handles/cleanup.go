package handles

import (
	"context"
	"time"

	"github.com/alist-org/alist/v3/internal/db"
	"github.com/alist-org/alist/v3/internal/fs"
	"github.com/alist-org/alist/v3/server/common"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// init 初始化清理功能
func init() {
	// 注册基础路径清理函数
	db.RegisterBasePathCleanupFunc(cleanupBasePathFolderAsync)
}

// CleanupBasePathFolder 清理基础路径文件夹的API端点
// 这个端点可以被管理员调用来手动清理被删除的基础路径文件夹
func CleanupBasePathFolder(c *gin.Context) {
	basePath := c.Query("path")
	if basePath == "" {
		common.ErrorStrResp(c, "path parameter is required", 400)
		return
	}

	// 检查用户权限（只有管理员可以执行清理操作）
	user := c.MustGet("user")
	if user == nil {
		common.ErrorStrResp(c, "unauthorized", 401)
		return
	}

	// 这里可以添加更严格的权限检查
	// 例如检查用户是否为管理员

	err := cleanupBasePathFolderSync(basePath)
	if err != nil {
		log.Errorf("Failed to delete folder for base path %s: %v", basePath, err)
		common.ErrorResp(c, err, 500)
		return
	}

	log.Infof("Successfully deleted folder for base path: %s", basePath)
	common.SuccessResp(c, gin.H{
		"message": "Folder deleted successfully",
		"path":    basePath,
	})
}



// BatchCleanupBasePathFolders 批量清理基础路径文件夹
func BatchCleanupBasePathFolders(c *gin.Context) {
	var req struct {
		Paths []string `json:"paths" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		common.ErrorResp(c, err, 400)
		return
	}

	// 检查用户权限
	user := c.MustGet("user")
	if user == nil {
		common.ErrorStrResp(c, "unauthorized", 401)
		return
	}

	var results []map[string]interface{}
	successCount := 0
	failureCount := 0

	for _, path := range req.Paths {
		result := map[string]interface{}{
			"path": path,
		}

		err := cleanupBasePathFolderSync(path)
		if err != nil {
			result["success"] = false
			result["error"] = err.Error()
			failureCount++
			log.Errorf("Failed to delete folder for base path %s: %v", path, err)
		} else {
			result["success"] = true
			successCount++
			log.Infof("Successfully deleted folder for base path: %s", path)
		}

		results = append(results, result)
	}

	common.SuccessResp(c, gin.H{
		"message":       "Batch cleanup completed",
		"total":         len(req.Paths),
		"success_count": successCount,
		"failure_count": failureCount,
		"results":       results,
	})
}

// cleanupBasePathFolderAsync 异步清理基础路径文件夹（由db层调用）
func cleanupBasePathFolderAsync(basePath string) {
	log.Infof("Starting async cleanup for base path: %s", basePath)

	// 检查路径是否有效
	if basePath == "" {
		log.Warnf("Base path is empty, skipping folder deletion")
		return
	}

	// 为了安全起见，我们只删除非根目录的路径
	if basePath == "/" {
		log.Warnf("Refusing to delete root path: %s", basePath)
		return
	}

	// 在goroutine中执行清理，避免阻塞用户更新操作
	go func() {
		if err := cleanupBasePathFolderSync(basePath); err != nil {
			log.Errorf("Async cleanup failed for base path %s: %v", basePath, err)
		} else {
			log.Infof("Async cleanup completed for base path: %s", basePath)
		}
	}()
}

// cleanupBasePathFolderSync 同步清理基础路径文件夹
func cleanupBasePathFolderSync(basePath string) error {
	log.Infof("Attempting to delete folder from storage for base path: %s", basePath)

	// 检查路径是否有效
	if basePath == "" {
		log.Warnf("Base path is empty, skipping folder deletion")
		return nil
	}

	// 为了安全起见，我们只删除非根目录的路径
	if basePath == "/" {
		log.Warnf("Refusing to delete root path: %s", basePath)
		return nil
	}

	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 检查文件夹是否存在
	_, err := fs.Get(ctx, basePath, &fs.GetArgs{})
	if err != nil {
		log.Infof("Base path folder %s does not exist or is not accessible, skipping deletion: %v", basePath, err)
		return nil
	}

	// 删除文件夹
	err = fs.Remove(ctx, basePath)
	if err != nil {
		return err
	}

	log.Infof("Successfully deleted folder for base path: %s", basePath)
	return nil
}

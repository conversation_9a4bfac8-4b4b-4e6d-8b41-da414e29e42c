package handles

import (
	"strconv"

	"github.com/alist-org/alist/v3/internal/cookie_manager"
	"github.com/alist-org/alist/v3/internal/model"
	"github.com/alist-org/alist/v3/server/common"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

// GetCookieStatus 获取Cookie状态
func GetCookieStatus(c *gin.Context) {
	// 只允许管理员访问
	user := c.MustGet("user").(*model.User)
	if !user.IsAdmin() {
		common.ErrorStrResp(c, "权限不足", 403)
		return
	}

	// 获取百度网盘Cookie状态
	statuses, err := cookie_manager.GetBaiduCookieStatus()
	if err != nil {
		common.ErrorResp(c, err, 500)
		return
	}

	common.SuccessResp(c, gin.H{
		"baidu_netdisk": statuses,
	})
}

// RefreshCookie 手动刷新Cookie
func RefreshCookie(c *gin.Context) {
	// 只允许管理员访问
	user := c.MustGet("user").(*model.User)
	if !user.IsAdmin() {
		common.ErrorStrResp(c, "权限不足", 403)
		return
	}

	// 获取存储ID
	storageIDStr := c.Param("storage_id")
	storageID, err := strconv.ParseUint(storageIDStr, 10, 32)
	if err != nil {
		common.ErrorStrResp(c, "无效的存储ID", 400)
		return
	}

	// 执行刷新
	if err := cookie_manager.RefreshBaiduCookie(uint(storageID)); err != nil {
		log.Errorf("手动刷新Cookie失败: %v", err)
		common.ErrorResp(c, err, 500)
		return
	}

	log.Infof("管理员手动刷新存储 %d 的Cookie成功", storageID)
	common.SuccessResp(c, gin.H{
		"message": "Cookie刷新成功",
	})
}

// GetCookieManagerStatus 获取Cookie管理器状态
func GetCookieManagerStatus(c *gin.Context) {
	// 只允许管理员访问
	user := c.MustGet("user").(*model.User)
	if !user.IsAdmin() {
		common.ErrorStrResp(c, "权限不足", 403)
		return
	}

	manager := cookie_manager.GetManager()
	
	common.SuccessResp(c, gin.H{
		"is_running": manager.IsStarted(),
		"message":    "Cookie管理器状态",
	})
}

// StartCookieManager 启动Cookie管理器
func StartCookieManager(c *gin.Context) {
	// 只允许管理员访问
	user := c.MustGet("user").(*model.User)
	if !user.IsAdmin() {
		common.ErrorStrResp(c, "权限不足", 403)
		return
	}

	cookie_manager.StartCookieManager()
	
	log.Info("管理员手动启动Cookie管理器")
	common.SuccessResp(c, gin.H{
		"message": "Cookie管理器已启动",
	})
}

// StopCookieManager 停止Cookie管理器
func StopCookieManager(c *gin.Context) {
	// 只允许管理员访问
	user := c.MustGet("user").(*model.User)
	if !user.IsAdmin() {
		common.ErrorStrResp(c, "权限不足", 403)
		return
	}

	cookie_manager.StopCookieManager()
	
	log.Info("管理员手动停止Cookie管理器")
	common.SuccessResp(c, gin.H{
		"message": "Cookie管理器已停止",
	})
}

package websocket

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"

	"github.com/alist-org/alist/v3/internal/model"
	"github.com/alist-org/alist/v3/internal/op"
	"github.com/alist-org/alist/v3/server/common"
)

// validateTokenAndGetUser 验证token并获取用户信息
func validateTokenAndGetUser(token string) (*model.User, error) {
	claims, err := common.ParseToken(token)
	if err != nil {
		return nil, err
	}

	user, err := op.GetUserByName(claims.Username)
	if err != nil {
		return nil, err
	}

	// 验证密码时间戳
	if claims.PwdTS != user.PwdTS {
		return nil, fmt.Errorf("password has been changed, login please")
	}

	// 检查用户是否被禁用
	if user.Disabled {
		return nil, fmt.Errorf("current user is disabled")
	}

	return user, nil
}

// HandleWebSocket 处理WebSocket连接
func HandleWebSocket(c *gin.Context) {
	// 从URL参数获取token并验证用户身份
	token := c.Query("token")
	if token == "" {
		log.Warn("WebSocket连接失败: token为空")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "token为空"})
		return
	}

	// 手动验证token并获取用户信息
	user, err := validateTokenAndGetUser(token)
	if err != nil {
		log.Warnf("WebSocket连接失败: token验证失败: %v", err)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "token验证失败"})
		return
	}

	if user == nil {
		log.Warn("WebSocket连接失败: 用户不存在")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "用户不存在"})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Errorf("WebSocket升级失败: %v", err)
		return
	}

	// 创建客户端
	client := &Client{
		ID:     uuid.New().String(),
		UserID: user.ID,
		Conn:   conn,
		Send:   make(chan []byte, 256),
		Hub:    GetGlobalHub(),
	}

	// 注册客户端
	client.Hub.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()

	log.Infof("WebSocket连接建立成功: UserID=%d, ClientID=%s", user.ID, client.ID)
}

// WebSocketNotifier WebSocket通知器接口
type WebSocketNotifier interface {
	NotifyCapacityUpdate(userID uint, data *CapacityUpdateData)
	NotifyCapacityCalculating(userID uint, basePath string, basePathID uint)
	NotifyCapacityCompleted(userID uint, basePath string, basePathID uint, usedBytes int64, totalFiles int64)
	NotifyCapacityFailed(userID uint, basePath string, basePathID uint, errorMsg string)
}

// DefaultWebSocketNotifier 默认WebSocket通知器实现
type DefaultWebSocketNotifier struct {
	hub *Hub
}

// NewWebSocketNotifier 创建WebSocket通知器
func NewWebSocketNotifier() WebSocketNotifier {
	return &DefaultWebSocketNotifier{
		hub: GetGlobalHub(),
	}
}

// NotifyCapacityUpdate 通知容量更新
func (n *DefaultWebSocketNotifier) NotifyCapacityUpdate(userID uint, data *CapacityUpdateData) {
	if n.hub == nil {
		log.Warn("WebSocket Hub未初始化，无法发送容量更新通知")
		return
	}

	log.Infof("发送容量更新通知: UserID=%d, BasePath=%s, Status=%s, UsedBytes=%d", 
		userID, data.BasePath, data.Status, data.UsedBytes)

	n.hub.SendCapacityUpdate(userID, data)
}

// NotifyCapacityCalculating 通知容量计算开始
func (n *DefaultWebSocketNotifier) NotifyCapacityCalculating(userID uint, basePath string, basePathID uint) {
	data := &CapacityUpdateData{
		BasePathID: basePathID,
		BasePath:   basePath,
		Status:     "calculating",
		UsedBytes:  0,
		TotalFiles: 0,
		Progress:   0.0,
	}
	n.NotifyCapacityUpdate(userID, data)
}

// NotifyCapacityCompleted 通知容量计算完成
func (n *DefaultWebSocketNotifier) NotifyCapacityCompleted(userID uint, basePath string, basePathID uint, usedBytes int64, totalFiles int64) {
	data := &CapacityUpdateData{
		BasePathID: basePathID,
		BasePath:   basePath,
		Status:     "completed",
		UsedBytes:  usedBytes,
		TotalFiles: totalFiles,
		Progress:   1.0,
	}
	n.NotifyCapacityUpdate(userID, data)

	// 通知后台清理管理器容量计算完成
	if capacityCompletedCallback != nil {
		go capacityCompletedCallback(userID, basePath)
	}
}

// NotifyCapacityFailed 通知容量计算失败
func (n *DefaultWebSocketNotifier) NotifyCapacityFailed(userID uint, basePath string, basePathID uint, errorMsg string) {
	data := &CapacityUpdateData{
		BasePathID: basePathID,
		BasePath:   basePath,
		Status:     "failed",
		UsedBytes:  0,
		TotalFiles: 0,
		Progress:   0.0,
	}
	n.NotifyCapacityUpdate(userID, data)
}

// GetWebSocketStats 获取WebSocket统计信息
func GetWebSocketStats(c *gin.Context) {
	hub := GetGlobalHub()
	if hub == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "WebSocket Hub未初始化"})
		return
	}

	stats := hub.GetStats()
	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// 全局WebSocket通知器实例
var globalNotifier WebSocketNotifier

// 容量计算完成回调函数类型
type CapacityCompletedCallback func(userID uint, basePath string)

// 全局容量计算完成回调函数
var capacityCompletedCallback CapacityCompletedCallback

// GetGlobalNotifier 获取全局WebSocket通知器
func GetGlobalNotifier() WebSocketNotifier {
	if globalNotifier == nil {
		globalNotifier = NewWebSocketNotifier()
		log.Info("WebSocket通知器已初始化")
	}
	return globalNotifier
}

// SetCapacityCompletedCallback 设置容量计算完成回调函数
func SetCapacityCompletedCallback(callback CapacityCompletedCallback) {
	capacityCompletedCallback = callback
	log.Info("容量计算完成回调函数已设置")
}

// CapacityExceededData 容量超限通知数据
type CapacityExceededData struct {
	FolderName  string   `json:"folder_name"`
	StorageType string   `json:"storage_type"`
	BasePath    string   `json:"base_path"`
	DeletedFiles []string `json:"deleted_files"`
}

// NotifyCapacityExceeded 发送容量超限通知
func NotifyCapacityExceeded(userID uint, folderName, storageType, basePath string, deletedFiles []string) {
	if globalNotifier == nil {
		log.Warn("WebSocket通知器未初始化，无法发送容量超限通知")
		return
	}

	data := &CapacityExceededData{
		FolderName:   folderName,
		StorageType:  storageType,
		BasePath:     basePath,
		DeletedFiles: deletedFiles,
	}

	message := &WSMessage{
		Type:      "capacity_exceeded",
		UserID:    userID,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	// 发送消息给指定用户
	hub := GetGlobalHub()
	if hub != nil {
		hub.SendToUser(userID, message)
		log.Infof("📢 已发送容量超限通知给用户 %d: %s", userID, folderName)
	} else {
		log.Warn("WebSocket Hub未初始化，无法发送容量超限通知")
	}
}

// InitWebSocket 初始化WebSocket服务
func InitWebSocket() {
	// 启动Hub
	GetGlobalHub()
	
	// 初始化通知器
	GetGlobalNotifier()
	
	log.Info("WebSocket服务初始化完成")
}

// CleanupWebSocket 清理WebSocket服务
func CleanupWebSocket() {
	if globalHub != nil {
		// 这里可以添加清理逻辑，比如关闭所有连接
		log.Info("WebSocket服务清理完成")
	}
}

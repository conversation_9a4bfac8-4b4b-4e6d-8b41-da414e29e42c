package websocket

import (
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	log "github.com/sirupsen/logrus"
)

// WSMessage WebSocket消息结构
type WSMessage struct {
	Type      string      `json:"type"`
	UserID    uint        `json:"user_id"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// CapacityUpdateData 容量更新数据
type CapacityUpdateData struct {
	BasePathID uint    `json:"base_path_id"`
	BasePath   string  `json:"base_path"`
	Status     string  `json:"status"`      // "calculating", "completed", "failed"
	UsedBytes  int64   `json:"used_bytes"`
	TotalFiles int64   `json:"total_files"`
	Progress   float64 `json:"progress"` // 0.0-1.0
}

// Client WebSocket客户端连接
type Client struct {
	ID     string
	UserID uint
	Conn   *websocket.Conn
	Send   chan []byte
	Hub    *Hub
}

// Hub WebSocket连接管理中心
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 按用户ID分组的客户端
	userClients map[uint][]*Client

	// 注册新客户端
	register chan *Client

	// 注销客户端
	unregister chan *Client

	// 广播消息到所有客户端
	broadcast chan []byte

	// 发送消息到指定用户
	userMessage chan UserMessage

	// 互斥锁
	mutex sync.RWMutex
}

// UserMessage 用户消息
type UserMessage struct {
	UserID  uint
	Message []byte
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源，生产环境应该更严格
		return true
	},
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		clients:     make(map[*Client]bool),
		userClients: make(map[uint][]*Client),
		register:    make(chan *Client),
		unregister:  make(chan *Client),
		broadcast:   make(chan []byte),
		userMessage: make(chan UserMessage),
	}
}

// Run 运行Hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case userMsg := <-h.userMessage:
			h.sendToUser(userMsg.UserID, userMsg.Message)
		}
	}
}

// registerClient 注册客户端
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true
	
	// 按用户ID分组
	if h.userClients[client.UserID] == nil {
		h.userClients[client.UserID] = make([]*Client, 0)
	}
	h.userClients[client.UserID] = append(h.userClients[client.UserID], client)

	log.Infof("WebSocket客户端注册成功: UserID=%d, ClientID=%s, 总连接数=%d", 
		client.UserID, client.ID, len(h.clients))
}

// unregisterClient 注销客户端
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.Send)

		// 从用户分组中移除
		if clients, exists := h.userClients[client.UserID]; exists {
			for i, c := range clients {
				if c == client {
					h.userClients[client.UserID] = append(clients[:i], clients[i+1:]...)
					break
				}
			}
			// 如果该用户没有其他连接，删除用户分组
			if len(h.userClients[client.UserID]) == 0 {
				delete(h.userClients, client.UserID)
			}
		}

		log.Infof("WebSocket客户端注销: UserID=%d, ClientID=%s, 剩余连接数=%d", 
			client.UserID, client.ID, len(h.clients))
	}
}

// broadcastMessage 广播消息到所有客户端
func (h *Hub) broadcastMessage(message []byte) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(h.clients, client)
		}
	}
}

// sendToUser 发送消息到指定用户的所有连接
func (h *Hub) sendToUser(userID uint, message []byte) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	clients, exists := h.userClients[userID]
	if !exists {
		log.Debugf("用户 %d 没有WebSocket连接", userID)
		return
	}

	log.Debugf("发送WebSocket消息到用户 %d，连接数: %d", userID, len(clients))

	for _, client := range clients {
		select {
		case client.Send <- message:
		default:
			// 发送失败，关闭连接
			close(client.Send)
			delete(h.clients, client)
		}
	}
}

// SendCapacityUpdate 发送容量更新消息到指定用户
func (h *Hub) SendCapacityUpdate(userID uint, data *CapacityUpdateData) {
	message := WSMessage{
		Type:      "capacity_update",
		UserID:    userID,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Errorf("序列化容量更新消息失败: %v", err)
		return
	}

	log.Infof("发送容量更新消息: UserID=%d, BasePath=%s, Status=%s", 
		userID, data.BasePath, data.Status)

	h.userMessage <- UserMessage{
		UserID:  userID,
		Message: messageBytes,
	}
}

// GetStats 获取Hub统计信息
func (h *Hub) GetStats() map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	userConnections := make(map[uint]int)
	for userID, clients := range h.userClients {
		userConnections[userID] = len(clients)
	}

	return map[string]interface{}{
		"total_connections":  len(h.clients),
		"total_users":        len(h.userClients),
		"user_connections":   userConnections,
	}
}

// 全局Hub实例
var globalHub *Hub

// GetGlobalHub 获取全局Hub实例
func GetGlobalHub() *Hub {
	if globalHub == nil {
		globalHub = NewHub()
		go globalHub.Run()
		log.Info("WebSocket Hub已启动")
	}
	return globalHub
}

// readPump 处理客户端读取
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	// 设置读取超时
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Errorf("WebSocket读取错误: %v", err)
			}
			break
		}

		// 处理客户端消息（如心跳）
		c.handleMessage(message)
	}
}

// writePump 处理客户端写入
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Errorf("WebSocket写入错误: %v", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端消息
func (c *Client) handleMessage(message []byte) {
	var msg map[string]interface{}
	if err := json.Unmarshal(message, &msg); err != nil {
		log.Errorf("解析WebSocket消息失败: %v", err)
		return
	}

	msgType, ok := msg["type"].(string)
	if !ok {
		return
	}

	switch msgType {
	case "ping":
		// 响应心跳
		pongMsg := map[string]interface{}{
			"type":      "pong",
			"timestamp": time.Now().Unix(),
		}
		if pongBytes, err := json.Marshal(pongMsg); err == nil {
			select {
			case c.Send <- pongBytes:
			default:
				close(c.Send)
			}
		}
	}
}

// SendToUser 公开方法：发送消息到指定用户
func (h *Hub) SendToUser(userID uint, message *WSMessage) {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Errorf("序列化WebSocket消息失败: %v", err)
		return
	}

	select {
	case h.userMessage <- UserMessage{UserID: userID, Message: messageBytes}:
	default:
		log.Warn("WebSocket用户消息通道已满，消息发送失败")
	}
}

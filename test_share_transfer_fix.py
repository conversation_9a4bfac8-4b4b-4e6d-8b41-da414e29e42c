#!/usr/bin/env python3
"""
测试分享转存修复效果的脚本
验证不会重复创建相同网盘类型的分享基础路径
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5244"
USERNAME = "8844"
PASSWORD = "8844"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 200:
            token = result["data"]["token"]
            print(f"✅ 登录成功，获取到token: {token[:50]}...")
            return token
        else:
            print(f"❌ 登录失败: {result.get('message')}")
            return None
    else:
        print(f"❌ 登录请求失败: {response.status_code}")
        return None

def get_user_info(token):
    """获取用户信息"""
    headers = {"Authorization": token}
    response = requests.get(f"{BASE_URL}/api/me", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 200:
            user_data = result["data"]
            print(f"✅ 获取用户信息成功: {user_data['username']}")
            print(f"📁 当前基础路径数量: {len(user_data.get('base_paths', []))}")
            
            # 显示现有的分享基础路径
            share_paths = [bp for bp in user_data.get('base_paths', []) if bp.get('type') == '分享']
            print(f"🔗 现有分享基础路径数量: {len(share_paths)}")
            for i, path in enumerate(share_paths):
                print(f"   {i+1}. {path.get('path')} (ID: {path.get('id')})")
            
            return user_data
        else:
            print(f"❌ 获取用户信息失败: {result.get('message')}")
            return None
    else:
        print(f"❌ 获取用户信息请求失败: {response.status_code}")
        return None

def test_share_transfer(token, share_links, baidu_cookie):
    """测试分享转存功能"""
    headers = {"Authorization": token}
    transfer_data = {
        "share_links": share_links,
        "baidu_cookie": baidu_cookie
    }
    
    print(f"🚀 开始测试分享转存...")
    print(f"📋 分享链接数量: {len(share_links)}")
    
    response = requests.post(f"{BASE_URL}/api/fs/share_transfer", json=transfer_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 200:
            transfer_result = result["data"]
            print(f"✅ 转存请求成功")
            print(f"📊 转存结果: 成功 {transfer_result.get('success', 0)}, 失败 {transfer_result.get('failed', 0)}")
            
            # 显示详细结果
            for i, msg in enumerate(transfer_result.get('results', [])):
                print(f"   {i+1}. {msg}")
            
            return True
        else:
            print(f"❌ 转存失败: {result.get('message')}")
            return False
    else:
        print(f"❌ 转存请求失败: {response.status_code}")
        return False

def main():
    """主测试流程"""
    print("🧪 开始测试分享转存修复效果")
    print("=" * 50)
    
    # 1. 登录
    token = login()
    if not token:
        return
    
    print("\n" + "=" * 50)
    
    # 2. 获取初始用户信息
    print("📋 获取初始用户信息...")
    initial_user_info = get_user_info(token)
    if not initial_user_info:
        return
    
    initial_share_paths = [bp for bp in initial_user_info.get('base_paths', []) if bp.get('type') == '分享']
    initial_share_count = len(initial_share_paths)
    
    print("\n" + "=" * 50)
    
    # 3. 第一次转存百度网盘链接
    print("🔄 第一次转存百度网盘分享链接...")
    baidu_cookie = "BAIDUID=test_cookie_value"  # 测试用的cookie
    baidu_links = [
        "https://pan.baidu.com/s/1test_link_1",
        "https://pan.baidu.com/s/1test_link_2"
    ]
    
    test_share_transfer(token, baidu_links, baidu_cookie)
    
    # 等待处理完成
    time.sleep(2)
    
    print("\n" + "=" * 50)
    
    # 4. 获取第一次转存后的用户信息
    print("📋 获取第一次转存后的用户信息...")
    first_user_info = get_user_info(token)
    if not first_user_info:
        return
    
    first_share_paths = [bp for bp in first_user_info.get('base_paths', []) if bp.get('type') == '分享']
    first_share_count = len(first_share_paths)
    
    print("\n" + "=" * 50)
    
    # 5. 第二次转存相同类型的百度网盘链接
    print("🔄 第二次转存百度网盘分享链接（测试重复创建问题）...")
    baidu_links_2 = [
        "https://pan.baidu.com/s/1test_link_3",
        "https://pan.baidu.com/s/1test_link_4"
    ]
    
    test_share_transfer(token, baidu_links_2, baidu_cookie)
    
    # 等待处理完成
    time.sleep(2)
    
    print("\n" + "=" * 50)
    
    # 6. 获取第二次转存后的用户信息
    print("📋 获取第二次转存后的用户信息...")
    final_user_info = get_user_info(token)
    if not final_user_info:
        return
    
    final_share_paths = [bp for bp in final_user_info.get('base_paths', []) if bp.get('type') == '分享']
    final_share_count = len(final_share_paths)
    
    print("\n" + "=" * 50)
    
    # 7. 分析测试结果
    print("📊 测试结果分析:")
    print(f"   初始分享基础路径数量: {initial_share_count}")
    print(f"   第一次转存后数量: {first_share_count}")
    print(f"   第二次转存后数量: {final_share_count}")
    
    if final_share_count == first_share_count:
        print("✅ 测试通过！第二次转存没有重复创建分享基础路径")
    else:
        print("❌ 测试失败！第二次转存重复创建了分享基础路径")
        print("   这表明修复可能不完全有效")
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()

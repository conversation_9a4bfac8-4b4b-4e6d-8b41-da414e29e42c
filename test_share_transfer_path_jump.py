#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分享转存路径跳转功能测试脚本
测试用户点击转存结果路径后能否正确跳转到文件夹
"""

import requests
import json
import time
import sys
from urllib.parse import urljoin

class ShareTransferPathJumpTest:
    def __init__(self, base_url="http://localhost:5244", username="admin", password="8844"):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.token = None
        
    def login(self):
        """登录获取token"""
        login_url = urljoin(self.base_url, "/api/auth/login")
        login_data = {
            "username": self.username,
            "password": self.password
        }
        
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                self.token = result["data"]["token"]
                # 使用正确的Authorization头格式
                self.session.headers.update({"Authorization": self.token})
                print(f"✅ 登录成功，用户: {self.username}")
                print(f"   Token: {self.token[:20]}...")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 登录请求失败: {e}")
            return False
    
    def get_user_info(self):
        """获取用户信息"""
        try:
            url = urljoin(self.base_url, "/api/me")
            response = self.session.get(url)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                user_data = result["data"]
                print(f"✅ 获取用户信息成功")
                print(f"   用户名: {user_data.get('username')}")
                print(f"   权限值: {user_data.get('permission')}")
                print(f"   基础路径数量: {len(user_data.get('base_paths', []))}")
                
                # 检查无密码访问权限
                permission = user_data.get('permission', 0)
                can_access_without_password = (permission >> 1) & 1 == 1
                print(f"   无密码访问权限: {'✅ 有' if can_access_without_password else '❌ 无'}")
                
                return user_data
            else:
                print(f"❌ 获取用户信息失败: {result.get('message')}")
                return None
                
        except Exception as e:
            print(f"❌ 获取用户信息请求失败: {e}")
            return None
    
    def test_share_transfer(self):
        """测试分享转存功能"""
        # 使用一个测试链接
        test_links = [
            "https://pan.baidu.com/s/1example_test_link"  # 这是一个示例链接，实际测试时需要替换
        ]
        
        try:
            url = urljoin(self.base_url, "/api/fs/share_transfer")
            data = {
                "share_links": test_links
            }
            
            print(f"🔄 开始测试分享转存...")
            response = self.session.post(url, json=data)
            response.raise_for_status()
            
            result = response.json()
            print(f"📊 转存API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("code") == 200:
                transfer_result = result["data"]
                print(f"✅ 转存请求成功")
                print(f"   成功: {transfer_result.get('success', 0)}")
                print(f"   失败: {transfer_result.get('failed', 0)}")
                print(f"   结果: {transfer_result.get('results', [])}")
                
                # 提取路径信息进行测试
                results = transfer_result.get('results', [])
                for result_str in results:
                    if ' -> ' in result_str:
                        parts = result_str.split(' -> ')
                        if len(parts) >= 2:
                            path = parts[1].strip()
                            print(f"🔍 发现转存路径: {path}")
                            self.test_path_access(path)
                
                return transfer_result
            else:
                print(f"❌ 转存失败: {result.get('message')}")
                return None
                
        except Exception as e:
            print(f"❌ 转存请求失败: {e}")
            return None
    
    def test_path_access(self, path):
        """测试路径访问"""
        try:
            url = urljoin(self.base_url, "/api/fs/list")
            data = {
                "path": path,
                "password": "",  # 测试无密码访问
                "page": 1,
                "per_page": 30,
                "refresh": False
            }
            
            print(f"🔍 测试路径访问: {path}")
            response = self.session.post(url, json=data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 200:
                content = result["data"]["content"]
                print(f"✅ 路径访问成功，文件数量: {len(content)}")
                return True
            elif result.get("code") == 403:
                print(f"❌ 路径访问被拒绝: {result.get('message')}")
                print(f"   这表明需要密码或权限不足")
                return False
            else:
                print(f"❌ 路径访问失败: {result.get('message')}")
                return False
                
        except Exception as e:
            print(f"❌ 路径访问请求失败: {e}")
            return False
    
    def test_virtual_path_conversion(self, original_path):
        """测试虚拟路径转换"""
        print(f"🔄 测试虚拟路径转换: {original_path}")
        
        # 模拟前端的路径转换逻辑
        user_info = self.get_user_info()
        if not user_info:
            return None
            
        base_paths = user_info.get('base_paths', [])
        shared_base_paths = [bp for bp in base_paths if bp.get('type') == '分享']
        
        print(f"   用户分享基础路径数量: {len(shared_base_paths)}")
        
        # 尝试匹配路径
        for i, base_path in enumerate(shared_base_paths):
            bp_path = base_path.get('path', '')
            if original_path.startswith(bp_path):
                relative_path = original_path[len(bp_path):]
                virtual_path = f"/shared/storage/{i + 1}{relative_path}"
                print(f"   匹配到基础路径 {i + 1}: {bp_path}")
                print(f"   转换为虚拟路径: {virtual_path}")
                
                # 测试虚拟路径访问
                return self.test_path_access(virtual_path)
        
        print(f"   未找到匹配的分享基础路径")
        return False
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始分享转存路径跳转功能测试")
        print("=" * 50)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 获取用户信息
        user_info = self.get_user_info()
        if not user_info:
            return False
        
        # 3. 测试分享转存（可选，需要真实链接）
        print("\n📝 注意: 分享转存测试需要真实的分享链接")
        print("   当前使用示例链接，可能会失败")
        print("   如需完整测试，请替换为真实的百度网盘分享链接")
        
        # 4. 测试虚拟路径转换
        print("\n🔄 测试虚拟路径转换逻辑...")
        test_paths = [
            "/path/to/share/folder",
            "/百度分享/test_folder"
        ]
        
        for test_path in test_paths:
            self.test_virtual_path_conversion(test_path)
        
        print("\n✅ 测试完成")
        return True

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5244"
    
    tester = ShareTransferPathJumpTest(base_url=base_url)
    tester.run_test()

if __name__ == "__main__":
    main()
